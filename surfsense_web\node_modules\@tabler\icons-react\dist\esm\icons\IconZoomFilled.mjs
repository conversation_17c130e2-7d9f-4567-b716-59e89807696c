/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZoomFilled = createReactComponent("filled", "zoom-filled", "IconZoomFilled", [["path", { "d": "M14 3.072a8 8 0 0 1 2.617 11.424l4.944 4.943a1.5 1.5 0 0 1 -2.008 2.225l-.114 -.103l-4.943 -4.944a8 8 0 0 1 -12.49 -6.332l-.006 -.285l.005 -.285a8 8 0 0 1 11.995 -6.643z", "key": "svg-0" }]]);

export { IconZoomFilled as default };
//# sourceMappingURL=IconZoomFilled.mjs.map
