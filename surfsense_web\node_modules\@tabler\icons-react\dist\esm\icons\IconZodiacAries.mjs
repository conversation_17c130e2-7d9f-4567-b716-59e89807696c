/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZodiacAries = createReactComponent("outline", "zodiac-aries", "IconZodiacAries", [["path", { "d": "M12 5a5 5 0 1 0 -4 8", "key": "svg-0" }], ["path", { "d": "M16 13a5 5 0 1 0 -4 -8", "key": "svg-1" }], ["path", { "d": "M12 21l0 -16", "key": "svg-2" }]]);

export { IconZodiacAries as default };
//# sourceMappingURL=IconZodiacAries.mjs.map
