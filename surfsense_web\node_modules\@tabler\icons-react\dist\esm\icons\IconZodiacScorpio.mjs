/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZodiacScorpio = createReactComponent("outline", "zodiac-scorpio", "IconZodiacScorpio", [["path", { "d": "M3 4a2 2 0 0 1 2 2v9", "key": "svg-0" }], ["path", { "d": "M5 6a2 2 0 0 1 4 0v9", "key": "svg-1" }], ["path", { "d": "M9 6a2 2 0 0 1 4 0v10a3 3 0 0 0 3 3h5l-3 -3m0 6l3 -3", "key": "svg-2" }]]);

export { IconZodiacScorpio as default };
//# sourceMappingURL=IconZodiacScorpio.mjs.map
