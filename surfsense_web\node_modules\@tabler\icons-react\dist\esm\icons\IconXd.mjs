/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconXd = createReactComponent("outline", "xd", "IconXd", [["path", { "d": "M6 8l4 8", "key": "svg-0" }], ["path", { "d": "M6 16l4 -8", "key": "svg-1" }], ["path", { "d": "M14 8v8h2a2 2 0 0 0 2 -2v-4a2 2 0 0 0 -2 -2h-2z", "key": "svg-2" }]]);

export { IconXd as default };
//# sourceMappingURL=IconXd.mjs.map
