DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/surfsense

SECRET_KEY=SECRET
NEXT_FRONTEND_URL=http://localhost:3000

#Auth
AUTH_TYPE=GOOGLE or LOCAL
# For Google Auth Only
GOOGLE_OAUTH_CLIENT_ID=924507538m
GOOGLE_OAUTH_CLIENT_SECRET=GOCSV

# Workspace Access Control
ENABLE_WORKSPACE_SHARING=true
DEFAULT_WORKSPACE_ACCESS=private
ALLOW_PUBLIC_WORKSPACES=true

#Embedding Model
EMBEDDING_MODEL=mixedbread-ai/mxbai-embed-large-v1

RERANKERS_MODEL_NAME=ms-marco-MiniLM-L-12-v2
RERANKERS_MODEL_TYPE=flashrank


#LiteLLM TTS Provider: https://docs.litellm.ai/docs/text_to_speech#supported-providers
TTS_SERVICE=openai/tts-1
#Respective TTS Service API
TTS_SERVICE_API_KEY=
#OPTIONAL: TTS Provider API Base
TTS_SERVICE_API_BASE=

#LiteLLM STT Provider: https://docs.litellm.ai/docs/audio_transcription#supported-providers
STT_SERVICE=openai/whisper-1
#Respective STT Service API
STT_SERVICE_API_KEY=""
#OPTIONAL: STT Provider API Base
STT_SERVICE_API_BASE=


FIRECRAWL_API_KEY=fcr-01J0000000000000000000000

#File Parser Service
ETL_SERVICE=UNSTRUCTURED or LLAMACLOUD
UNSTRUCTURED_API_KEY=Tpu3P0U8iy
LLAMA_CLOUD_API_KEY=llx-nnn

#OPTIONAL: Add these for LangSmith Observability
LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT=https://api.smith.langchain.com
LANGSMITH_API_KEY=lsv2_pt_.....
LANGSMITH_PROJECT=surfsense


