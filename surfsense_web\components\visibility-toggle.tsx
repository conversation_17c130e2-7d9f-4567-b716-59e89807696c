"use client";

import { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Globe, Lock, Info, AlertTriangle } from "lucide-react";
import { toast } from "sonner";

interface VisibilityToggleProps {
  searchSpaceId: number;
  isPublic: boolean;
  onVisibilityChange?: (isPublic: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export function VisibilityToggle({
  searchSpaceId,
  isPublic,
  onVisibilityChange,
  disabled = false,
  className = ""
}: VisibilityToggleProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingVisibility, setPendingVisibility] = useState<boolean | null>(null);

  const handleVisibilityToggle = async (newVisibility: boolean) => {
    // If making public, show confirmation dialog
    if (newVisibility && !isPublic) {
      setPendingVisibility(newVisibility);
      setShowConfirmDialog(true);
      return;
    }

    // If making private, proceed directly
    await updateVisibility(newVisibility);
  };

  const updateVisibility = async (newVisibility: boolean) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FASTAPI_BACKEND_URL}/api/v1/searchspaces/${searchSpaceId}/visibility`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('surfsense_bearer_token')}`,
          },
          body: JSON.stringify({ is_public: newVisibility }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to update visibility');
      }

      const result = await response.json();
      
      toast.success(result.message, {
        description: newVisibility 
          ? "Your Knowledge Space is now discoverable by other users"
          : "Your Knowledge Space is now private"
      });

      onVisibilityChange?.(newVisibility);
    } catch (error: any) {
      console.error('Error updating visibility:', error);
      toast.error("Failed to update visibility", {
        description: error.message || "Please try again later"
      });
    } finally {
      setIsLoading(false);
      setShowConfirmDialog(false);
      setPendingVisibility(null);
    }
  };

  const handleConfirmVisibilityChange = () => {
    if (pendingVisibility !== null) {
      updateVisibility(pendingVisibility);
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isPublic ? (
            <Globe className="h-5 w-5 text-green-500" />
          ) : (
            <Lock className="h-5 w-5 text-gray-500" />
          )}
          Visibility Settings
          <Badge variant={isPublic ? "default" : "secondary"}>
            {isPublic ? "Public" : "Private"}
          </Badge>
        </CardTitle>
        <CardDescription>
          Control who can discover and access your Knowledge Space
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="visibility-toggle" className="text-sm font-medium">
              Make Knowledge Space Public
            </Label>
            <p className="text-xs text-muted-foreground">
              {isPublic 
                ? "Other users can discover and access this Knowledge Space"
                : "Only you and users you've shared with can access this Knowledge Space"
              }
            </p>
          </div>
          <Switch
            id="visibility-toggle"
            checked={isPublic}
            onCheckedChange={handleVisibilityToggle}
            disabled={disabled || isLoading}
          />
        </div>

        <div className="flex items-start gap-2 p-3 bg-muted/50 rounded-lg">
          <Info className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
          <div className="text-xs text-muted-foreground">
            <p className="font-medium mb-1">What does this mean?</p>
            <ul className="space-y-1">
              <li>• <strong>Private:</strong> Only you and explicitly shared users can access</li>
              <li>• <strong>Public:</strong> Discoverable by all users with read access</li>
              <li>• You can always change this setting later</li>
            </ul>
          </div>
        </div>

        <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-amber-500" />
                Make Knowledge Space Public?
              </AlertDialogTitle>
              <AlertDialogDescription className="space-y-2">
                <p>
                  You're about to make this Knowledge Space public. This means:
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Other users will be able to discover this Knowledge Space</li>
                  <li>They will have read access to documents and content</li>
                  <li>Your Knowledge Space will appear in public listings</li>
                  <li>You can change this back to private at any time</li>
                </ul>
                <p className="text-sm font-medium">
                  Are you sure you want to continue?
                </p>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleConfirmVisibilityChange}>
                Make Public
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
}
