from datetime import datetime
import uuid
from typing import Optional, Literal
from pydantic import BaseModel, ConfigDict
from .base import IDModel, TimestampModel

PermissionLevel = Literal["read", "write", "admin"]

class SearchSpacePermissionBase(BaseModel):
    permission_level: PermissionLevel = "read"

class SearchSpacePermissionCreate(SearchSpacePermissionBase):
    user_id: uuid.UUID
    search_space_id: int

class SearchSpacePermissionUpdate(BaseModel):
    permission_level: Optional[PermissionLevel] = None

class SearchSpacePermissionRead(SearchSpacePermissionBase, IDModel, TimestampModel):
    id: int
    search_space_id: int
    user_id: uuid.UUID
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

class SearchSpaceAccessSettings(BaseModel):
    is_public: Optional[bool] = None
    allow_all_users: Optional[bool] = None

class SearchSpaceVisibilityToggle(BaseModel):
    is_public: bool

class GrantPermissionRequest(BaseModel):
    user_id: uuid.UUID
    permission_level: PermissionLevel

class RevokePermissionRequest(BaseModel):
    user_id: uuid.UUID

class SearchSpaceAccessInfo(BaseModel):
    """Information about a user's access to a search space"""
    search_space_id: int
    user_id: uuid.UUID
    access_type: Literal["owner", "permission", "public", "all_users"]
    permission_level: Optional[PermissionLevel] = None
    can_read: bool
    can_write: bool
    can_admin: bool

class WorkspaceAccessConfig(BaseModel):
    """System-wide workspace access configuration"""
    enable_workspace_sharing: bool
    default_workspace_access: Literal["private", "public", "all_users"]
    allow_public_workspaces: bool
