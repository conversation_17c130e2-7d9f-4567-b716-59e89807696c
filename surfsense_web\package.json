{
  "name": "surfsense_web",
  "version": "0.0.7",
  "private": true,
  "description": "SurfSense Frontend",
  "scripts": {
    "dev": "next dev",
    "dev:turbopack": "next dev --turbopack",
    "build": "next build",
    "build:static": "next build",
    "start": "next start",
    "lint": "next lint",
    "debug": "cross-env NODE_OPTIONS=--inspect next dev --turbopack",
    "debug:browser": "cross-env NODE_OPTIONS=--inspect next dev --turbopack",
    "debug:server": "cross-env NODE_OPTIONS=--inspect=0.0.0.0:9229 next dev --turbopack",

  },
  "dependencies": {
    "@ai-sdk/react": "^1.1.21",
    "@hookform/resolvers": "^4.1.3",
    "@radix-ui/react-accordion": "^1.2.3",
    "@radix-ui/react-alert-dialog": "^1.1.6",
    "@radix-ui/react-avatar": "^1.1.3",
    "@radix-ui/react-checkbox": "^1.1.4",
    "@radix-ui/react-collapsible": "^1.1.3",
    "@radix-ui/react-dialog": "^1.1.6",
    "@radix-ui/react-dropdown-menu": "^2.1.6",
    "@radix-ui/react-label": "^2.1.2",
    "@radix-ui/react-popover": "^1.1.6",
    "@radix-ui/react-select": "^2.1.6",
    "@radix-ui/react-separator": "^1.1.2",
    "@radix-ui/react-slider": "^1.3.4",
    "@radix-ui/react-slot": "^1.1.2",
    "@radix-ui/react-switch": "^1.2.5",
    "@radix-ui/react-tabs": "^1.1.3",
    "@radix-ui/react-tooltip": "^1.1.8",
    "@tabler/icons-react": "^3.30.0",
    "@tanstack/react-table": "^8.21.2",
    "@types/mdx": "^2.0.13",
    "@types/react-syntax-highlighter": "^15.5.13",
    "ai": "^4.1.54",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "date-fns": "^4.1.0",
    "emblor": "^1.4.7",
    "framer-motion": "^12.4.7",

    "geist": "^1.3.1",
    "lucide-react": "^0.477.0",
    "next": "15.2.3",
    "next-themes": "^0.4.4",
    "react": "^19.0.0",
    "react-day-picker": "^9.7.0",
    "react-dom": "^19.0.0",
    "react-dropzone": "^14.3.8",
    "react-hook-form": "^7.54.2",
    "react-json-view": "^1.21.3",
    "react-json-view-lite": "^2.4.0",
    "react-markdown": "^10.0.1",
    "react-syntax-highlighter": "^15.6.1",
    "rehype-raw": "^7.0.0",
    "rehype-sanitize": "^6.0.0",
    "remark-gfm": "^4.0.1",
    "sonner": "^2.0.1",
    "tailwind-merge": "^3.0.2",
    "tailwindcss-animate": "^1.0.7",
    "zod": "^3.24.2"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3",
    "@tailwindcss/postcss": "^4",
    "@tailwindcss/typography": "^0.5.16",
    "@types/node": "^20.17.22",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "cross-env": "^7.0.3",
    "eslint": "^9",
    "eslint-config-next": "15.2.0",
    "tailwindcss": "^4",
    "typescript": "^5"
  }
}
