from fastapi import HTTPException
from sqlalchemy.ext.async<PERSON> import Async<PERSON>ession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from app.db import User, SearchSpace, SearchSpacePermission
from app.config import config
from typing import Optional, Literal

PermissionLevel = Literal["read", "write", "admin"]

async def check_search_space_access(
    session: AsyncSession, 
    search_space_id: int, 
    user: User, 
    required_permission: PermissionLevel = "read"
) -> SearchSpace:
    """
    Enhanced access control for search spaces that supports:
    - Owner access (full permissions)
    - Shared access via permissions table
    - Public workspaces (if enabled)
    - All-users access (if enabled)
    - Global workspace sharing configuration
    """
    
    # First, get the search space with all related permissions
    result = await session.execute(
        select(SearchSpace)
        .options(selectinload(SearchSpace.search_space_permissions))
        .filter(SearchSpace.id == search_space_id)
    )
    search_space = result.scalars().first()
    
    if not search_space:
        raise HTTPException(status_code=404, detail="Search space not found")
    
    # Check if user is the owner (always has full access)
    if search_space.user_id == user.id:
        return search_space
    
    # If workspace sharing is disabled globally, only owners can access
    if not config.ENABLE_WORKSPACE_SHARING:
        raise HTTPException(
            status_code=403, 
            detail="You don't have permission to access this search space"
        )
    
    # Check if workspace allows all users
    if search_space.allow_all_users:
        return search_space
    
    # Check if workspace is public and public access is enabled
    if search_space.is_public and config.ALLOW_PUBLIC_WORKSPACES:
        return search_space
    
    # Check explicit permissions
    for permission in search_space.search_space_permissions:
        if permission.user_id == user.id:
            # Check if user has required permission level
            if _has_required_permission(permission.permission_level, required_permission):
                return search_space
            else:
                raise HTTPException(
                    status_code=403, 
                    detail=f"Insufficient permissions. {required_permission} access required"
                )
    
    # No access found
    raise HTTPException(
        status_code=403, 
        detail="You don't have permission to access this search space"
    )

def _has_required_permission(user_permission: str, required_permission: PermissionLevel) -> bool:
    """
    Check if user's permission level meets the required permission level.
    Permission hierarchy: read < write < admin
    """
    permission_levels = {"read": 1, "write": 2, "admin": 3}
    
    user_level = permission_levels.get(user_permission, 0)
    required_level = permission_levels.get(required_permission, 0)
    
    return user_level >= required_level

async def grant_search_space_permission(
    session: AsyncSession,
    search_space_id: int,
    target_user_id: str,
    permission_level: PermissionLevel,
    granting_user: User
) -> SearchSpacePermission:
    """
    Grant permission to a user for a search space.
    Only owners and admins can grant permissions.
    """
    
    # Check if granting user has admin access to the search space
    search_space = await check_search_space_access(
        session, search_space_id, granting_user, "admin"
    )
    
    # Check if permission already exists
    existing_permission = await session.execute(
        select(SearchSpacePermission).filter(
            SearchSpacePermission.search_space_id == search_space_id,
            SearchSpacePermission.user_id == target_user_id
        )
    )
    existing = existing_permission.scalars().first()
    
    if existing:
        # Update existing permission
        existing.permission_level = permission_level
        await session.commit()
        await session.refresh(existing)
        return existing
    else:
        # Create new permission
        new_permission = SearchSpacePermission(
            search_space_id=search_space_id,
            user_id=target_user_id,
            permission_level=permission_level
        )
        session.add(new_permission)
        await session.commit()
        await session.refresh(new_permission)
        return new_permission

async def revoke_search_space_permission(
    session: AsyncSession,
    search_space_id: int,
    target_user_id: str,
    revoking_user: User
) -> bool:
    """
    Revoke permission from a user for a search space.
    Only owners and admins can revoke permissions.
    """
    
    # Check if revoking user has admin access to the search space
    await check_search_space_access(session, search_space_id, revoking_user, "admin")
    
    # Find and delete the permission
    permission = await session.execute(
        select(SearchSpacePermission).filter(
            SearchSpacePermission.search_space_id == search_space_id,
            SearchSpacePermission.user_id == target_user_id
        )
    )
    permission_obj = permission.scalars().first()
    
    if permission_obj:
        await session.delete(permission_obj)
        await session.commit()
        return True
    
    return False

async def update_search_space_access_settings(
    session: AsyncSession,
    search_space_id: int,
    user: User,
    is_public: Optional[bool] = None,
    allow_all_users: Optional[bool] = None
) -> SearchSpace:
    """
    Update access settings for a search space.
    Only owners can modify access settings.
    """
    
    # Check if user has admin access (owner or admin permission)
    search_space = await check_search_space_access(session, search_space_id, user, "admin")
    
    # Update settings
    if is_public is not None:
        if not config.ALLOW_PUBLIC_WORKSPACES and is_public:
            raise HTTPException(
                status_code=400,
                detail="Public workspaces are disabled by system configuration"
            )
        search_space.is_public = is_public
    
    if allow_all_users is not None:
        if not config.ENABLE_WORKSPACE_SHARING and allow_all_users:
            raise HTTPException(
                status_code=400,
                detail="Workspace sharing is disabled by system configuration"
            )
        search_space.allow_all_users = allow_all_users
    
    await session.commit()
    await session.refresh(search_space)
    return search_space

# Backward compatibility function - can be used to replace check_ownership
async def check_ownership(session: AsyncSession, model, item_id: int, user: User):
    """
    Backward compatibility wrapper for the old check_ownership function.
    For SearchSpace, uses the new access control. For other models, uses original logic.
    """
    if model == SearchSpace:
        return await check_search_space_access(session, item_id, user, "write")
    else:
        # Original ownership check for other models
        item = await session.execute(select(model).filter(model.id == item_id, model.user_id == user.id))
        item = item.scalars().first()
        if not item:
            raise HTTPException(status_code=404, detail="Item not found or you don't have permission to access it")
        return item
