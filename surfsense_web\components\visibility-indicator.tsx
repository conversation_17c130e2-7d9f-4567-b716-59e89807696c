"use client";

import { Badge } from "@/components/ui/badge";
import { Globe, Lock } from "lucide-react";
import { cn } from "@/lib/utils";

interface VisibilityIndicatorProps {
  isPublic: boolean;
  className?: string;
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
  showText?: boolean;
}

export function VisibilityIndicator({
  isPublic,
  className = "",
  size = "md",
  showIcon = true,
  showText = true
}: VisibilityIndicatorProps) {
  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    md: "text-sm px-2.5 py-1.5",
    lg: "text-base px-3 py-2"
  };

  const iconSizes = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5"
  };

  return (
    <Badge 
      variant={isPublic ? "default" : "secondary"} 
      className={cn(sizeClasses[size], className)}
    >
      {showIcon && (
        <>
          {isPublic ? (
            <Globe className={cn(iconSizes[size], showText && "mr-1")} />
          ) : (
            <Lock className={cn(iconSizes[size], showText && "mr-1")} />
          )}
        </>
      )}
      {showText && (isPublic ? "Public" : "Private")}
    </Badge>
  );
}
