"use client";

import { Suspense } from 'react';
import ChatsPageClient from './chats-client';
import { useParams } from 'next/navigation';

export default function ChatsPage() {
  const params = useParams();
  const searchSpaceId = params.search_space_id as string;

  return (
    <Suspense fallback={<div className="flex items-center justify-center h-[60vh]">
      <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
    </div>}>
      <ChatsPageClient searchSpaceId={searchSpaceId} />
    </Suspense>
  );
}