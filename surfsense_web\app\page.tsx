"use client";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Navbar } from "@/components/Navbar";
import { motion } from "framer-motion";
import { ModernHeroWithGradients } from "@/components/ModernHeroWithGradients";
import { Footer } from "@/components/Footer";
import { useCurrentUser } from "@/hooks/use-current-user";

export default function HomePage() {
  const router = useRouter();
  const { user, loading } = useCurrentUser();
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return;

    // Check if user has a token in localStorage
    const token = localStorage.getItem('surfsense_bearer_token');

    if (token && user) {
      // User is authenticated, redirect to dashboard
      router.push('/dashboard');
      return;
    }

    // If no token or user data loading is complete, stop checking
    if (!token || !loading) {
      setIsCheckingAuth(false);
    }
  }, [user, loading, router]);

  // Show loading state while checking authentication
  if (isCheckingAuth || (typeof window !== 'undefined' && localStorage.getItem('surfsense_bearer_token') && loading)) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 text-gray-900 dark:from-black dark:to-gray-900 dark:text-white flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4"></div>
          <p className="text-muted-foreground">Checking authentication...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 text-gray-900 dark:from-black dark:to-gray-900 dark:text-white">
      <Navbar />
      <ModernHeroWithGradients />
      <Footer />
    </main>
  );
}