"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { VisibilityIndicator } from "@/components/visibility-indicator";
import { 
  Search, 
  Globe, 
  Users, 
  Calendar,
  ExternalLink,
  Filter,
  SortAsc,
  SortDesc
} from "lucide-react";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface PublicSpace {
  id: number;
  name: string;
  description: string;
  created_at: string;
  is_public: boolean;
  user_id: string;
  owner_name?: string;
  document_count?: number;
  last_activity?: string;
}

export function PublicSpacesBrowser() {
  const [publicSpaces, setPublicSpaces] = useState<PublicSpace[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<"name" | "created_at" | "activity">("created_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [error, setError] = useState<string | null>(null);

  // Mock data for demonstration
  const mockPublicSpaces: PublicSpace[] = [
    {
      id: 1,
      name: "AI Research Papers",
      description: "Collection of cutting-edge AI research papers and findings",
      created_at: "2024-01-15T10:00:00Z",
      is_public: true,
      user_id: "user1",
      owner_name: "Dr. Sarah Chen",
      document_count: 45,
      last_activity: "2024-01-20T15:30:00Z"
    },
    {
      id: 2,
      name: "Web Development Resources",
      description: "Comprehensive collection of web development tutorials, frameworks, and best practices",
      created_at: "2024-01-10T14:20:00Z",
      is_public: true,
      user_id: "user2",
      owner_name: "Alex Rodriguez",
      document_count: 78,
      last_activity: "2024-01-19T09:15:00Z"
    },
    {
      id: 3,
      name: "Climate Change Data",
      description: "Scientific data and reports on climate change impacts and solutions",
      created_at: "2024-01-05T08:45:00Z",
      is_public: true,
      user_id: "user3",
      owner_name: "Prof. Maria Johnson",
      document_count: 32,
      last_activity: "2024-01-18T16:45:00Z"
    },
    {
      id: 4,
      name: "Startup Pitch Decks",
      description: "Collection of successful startup pitch decks and fundraising materials",
      created_at: "2024-01-12T11:30:00Z",
      is_public: true,
      user_id: "user4",
      owner_name: "David Kim",
      document_count: 23,
      last_activity: "2024-01-21T12:00:00Z"
    }
  ];

  useEffect(() => {
    // Simulate API call
    const fetchPublicSpaces = async () => {
      try {
        setLoading(true);
        // In real implementation, this would be:
        // const response = await fetch('/api/v1/searchspaces/public');
        // const data = await response.json();
        
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        setPublicSpaces(mockPublicSpaces);
        setError(null);
      } catch (err) {
        setError("Failed to load public spaces");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchPublicSpaces();
  }, []);

  const filteredAndSortedSpaces = publicSpaces
    .filter(space => 
      space.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      space.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      space.owner_name?.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "created_at":
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
          break;
        case "activity":
          const aActivity = a.last_activity ? new Date(a.last_activity).getTime() : 0;
          const bActivity = b.last_activity ? new Date(b.last_activity).getTime() : 0;
          comparison = aActivity - bActivity;
          break;
      }
      
      return sortOrder === "asc" ? comparison : -comparison;
    });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse space-y-4">
          <div className="h-10 bg-muted rounded"></div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className="h-48 bg-muted rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="text-center py-8">
        <CardContent>
          <p className="text-destructive">{error}</p>
          <Button 
            onClick={() => window.location.reload()} 
            className="mt-4"
          >
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center gap-2">
          <Globe className="h-6 w-6 text-blue-500" />
          <h2 className="text-2xl font-bold">Public Knowledge Spaces</h2>
          <Badge variant="secondary">{publicSpaces.length} spaces</Badge>
        </div>
        <p className="text-muted-foreground">
          Discover and explore public Knowledge Spaces shared by the community
        </p>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search spaces, descriptions, or owners..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="created_at">Created Date</SelectItem>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="activity">Last Activity</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="icon"
            onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
          >
            {sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Results */}
      {filteredAndSortedSpaces.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No spaces found</h3>
            <p className="text-muted-foreground">
              {searchQuery ? "Try adjusting your search terms" : "No public spaces available"}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredAndSortedSpaces.map((space, index) => (
            <motion.div
              key={space.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg line-clamp-1">{space.name}</CardTitle>
                      <CardDescription className="line-clamp-2 mt-1">
                        {space.description}
                      </CardDescription>
                    </div>
                    <VisibilityIndicator isPublic={space.is_public} size="sm" />
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      <span>{space.owner_name}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(space.created_at)}</span>
                    </div>
                  </div>
                  
                  {space.document_count && (
                    <div className="text-sm text-muted-foreground">
                      {space.document_count} documents
                    </div>
                  )}
                  
                  <div className="flex justify-between items-center pt-2">
                    <div className="text-xs text-muted-foreground">
                      {space.last_activity && (
                        <>Last activity: {formatDate(space.last_activity)}</>
                      )}
                    </div>
                    <Link href={`/dashboard/${space.id}/documents`}>
                      <Button size="sm" variant="outline">
                        <ExternalLink className="h-3 w-3 mr-1" />
                        View
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
