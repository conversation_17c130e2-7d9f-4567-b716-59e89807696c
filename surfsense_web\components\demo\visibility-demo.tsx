"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { VisibilityToggle } from "@/components/visibility-toggle";
import { VisibilityIndicator } from "@/components/visibility-indicator";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

export function VisibilityDemo() {
  const [demoSpaces, setDemoSpaces] = useState([
    { id: 1, name: "Research Papers", isPublic: false },
    { id: 2, name: "Company Knowledge Base", isPublic: true },
    { id: 3, name: "Personal Notes", isPublic: false },
  ]);

  const handleVisibilityChange = (id: number, isPublic: boolean) => {
    setDemoSpaces(prev => 
      prev.map(space => 
        space.id === id ? { ...space, isPublic } : space
      )
    );
  };

  return (
    <div className="space-y-6 p-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Knowledge Space Visibility Demo</h2>
        <p className="text-muted-foreground">
          This demonstrates the new visibility toggle feature for Knowledge Spaces
        </p>
      </div>

      <Separator />

      {/* Visibility Indicators Demo */}
      <Card>
        <CardHeader>
          <CardTitle>Visibility Indicators</CardTitle>
          <CardDescription>
            Visual indicators show the current visibility status of Knowledge Spaces
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm">Public:</span>
              <VisibilityIndicator isPublic={true} size="sm" />
              <VisibilityIndicator isPublic={true} size="md" />
              <VisibilityIndicator isPublic={true} size="lg" />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm">Private:</span>
              <VisibilityIndicator isPublic={false} size="sm" />
              <VisibilityIndicator isPublic={false} size="md" />
              <VisibilityIndicator isPublic={false} size="lg" />
            </div>
          </div>
          
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm">Icon only:</span>
              <VisibilityIndicator isPublic={true} showText={false} />
              <VisibilityIndicator isPublic={false} showText={false} />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm">Text only:</span>
              <VisibilityIndicator isPublic={true} showIcon={false} />
              <VisibilityIndicator isPublic={false} showIcon={false} />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Demo Knowledge Spaces */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {demoSpaces.map((space) => (
          <Card key={space.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{space.name}</CardTitle>
                <VisibilityIndicator isPublic={space.isPublic} size="sm" />
              </div>
            </CardHeader>
            <CardContent>
              <VisibilityToggle
                searchSpaceId={space.id}
                isPublic={space.isPublic}
                onVisibilityChange={(isPublic) => handleVisibilityChange(space.id, isPublic)}
                className="border-0 shadow-none p-0"
              />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Feature Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Feature Summary</CardTitle>
          <CardDescription>
            What's included in the Knowledge Space visibility feature
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-medium text-green-600">✅ Implemented</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Database schema with is_public field</li>
                <li>• API endpoints for visibility toggle</li>
                <li>• Access control logic</li>
                <li>• Visibility toggle component</li>
                <li>• Visual indicators throughout UI</li>
                <li>• Settings page for Knowledge Spaces</li>
                <li>• Confirmation dialogs for public changes</li>
                <li>• Form integration for new spaces</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-blue-600">🔧 Features</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Toggle between public/private modes</li>
                <li>• Clear visual feedback</li>
                <li>• Confirmation for making spaces public</li>
                <li>• Help text explaining implications</li>
                <li>• Proper error handling</li>
                <li>• Responsive design</li>
                <li>• Accessibility support</li>
                <li>• Integration with existing UI</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
