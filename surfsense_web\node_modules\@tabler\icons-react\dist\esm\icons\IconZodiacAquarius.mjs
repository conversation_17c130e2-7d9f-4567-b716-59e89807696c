/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZodiacAquarius = createReactComponent("outline", "zodiac-aquarius", "IconZodiacAquarius", [["path", { "d": "M3 10l3 -3l3 3l3 -3l3 3l3 -3l3 3", "key": "svg-0" }], ["path", { "d": "M3 17l3 -3l3 3l3 -3l3 3l3 -3l3 3", "key": "svg-1" }]]);

export { IconZodiacAquarius as default };
//# sourceMappingURL=IconZodiacAquarius.mjs.map
