from datetime import datetime
import uuid
from typing import Optional, List
from pydantic import BaseModel, ConfigDict
from .base import ID<PERSON>odel, TimestampModel

class SearchSpaceBase(BaseModel):
    name: str
    description: Optional[str] = None

class SearchSpaceCreate(SearchSpaceBase):
    is_public: Optional[bool] = False
    allow_all_users: Optional[bool] = False

class SearchSpaceUpdate(SearchSpaceBase):
    is_public: Optional[bool] = None
    allow_all_users: Optional[bool] = None

class SearchSpaceRead(SearchSpaceBase, IDModel, TimestampModel):
    id: int
    created_at: datetime
    user_id: uuid.UUID
    is_public: bool
    allow_all_users: bool

    model_config = ConfigDict(from_attributes=True)

class SearchSpaceWithPermissions(SearchSpaceRead):
    """Extended search space info including permission details"""
    permissions: List['SearchSpacePermissionRead'] = []
    user_access_level: Optional[str] = None  # owner, read, write, admin

    model_config = ConfigDict(from_attributes=True)

# Import here to avoid circular imports
from .access_control import SearchSpacePermissionRead
SearchSpaceWithPermissions.model_rebuild()