/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZoomQuestion = createReactComponent("outline", "zoom-question", "IconZoomQuestion", [["path", { "d": "M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0", "key": "svg-0" }], ["path", { "d": "M21 21l-6 -6", "key": "svg-1" }], ["path", { "d": "M10 13l0 .01", "key": "svg-2" }], ["path", { "d": "M10 10a1.5 1.5 0 1 0 -1.14 -2.474", "key": "svg-3" }]]);

export { IconZoomQuestion as default };
//# sourceMappingURL=IconZoomQuestion.mjs.map
