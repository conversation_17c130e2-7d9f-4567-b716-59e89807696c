/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZodiacLibra = createReactComponent("outline", "zodiac-libra", "IconZodiacLibra", [["path", { "d": "M5 20l14 0", "key": "svg-0" }], ["path", { "d": "M5 17h5v-.3a7 7 0 1 1 4 0v.3h5", "key": "svg-1" }]]);

export { IconZodiacLibra as default };
//# sourceMappingURL=IconZodiacLibra.mjs.map
