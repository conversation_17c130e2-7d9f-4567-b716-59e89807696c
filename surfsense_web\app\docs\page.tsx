"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ExternalLink, Book, Download, Settings } from "lucide-react";

export default function DocsPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">SurfSense Documentation</h1>
          <p className="text-xl text-muted-foreground">
            Get started with SurfSense - your AI-powered research and knowledge management assistant
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Book className="h-5 w-5" />
                Getting Started
              </CardTitle>
              <CardDescription>
                Learn the basics of setting up and using SurfSense
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                SurfSense is an AI-powered research assistant that integrates with tools like Notion, GitHub, Slack, and more to help you efficiently manage, search, and chat with your documents.
              </p>
              <Button variant="outline" className="w-full">
                <ExternalLink className="h-4 w-4 mr-2" />
                View Full Documentation
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Installation
              </CardTitle>
              <CardDescription>
                Multiple ways to install and deploy SurfSense
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium">Docker Installation (Recommended)</h4>
                  <p className="text-sm text-muted-foreground">Quick setup using Docker Compose</p>
                </div>
                <div>
                  <h4 className="font-medium">Manual Installation</h4>
                  <p className="text-sm text-muted-foreground">Install from source for development</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Configuration
              </CardTitle>
              <CardDescription>
                Configure your SurfSense instance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Learn how to configure LLM providers, set up connectors, and customize your SurfSense experience.
              </p>
              <Button variant="outline" className="w-full">
                <ExternalLink className="h-4 w-4 mr-2" />
                Configuration Guide
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Features</CardTitle>
              <CardDescription>
                Explore what SurfSense can do
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• Document upload and management</li>
                <li>• AI-powered search and chat</li>
                <li>• Podcast generation</li>
                <li>• Integration with external tools</li>
                <li>• Hybrid search capabilities</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        <div className="text-center">
          <p className="text-muted-foreground mb-4">
            Need help? Check out our GitHub repository or join our community.
          </p>
          <div className="flex justify-center gap-4">
            <Button variant="outline">
              <ExternalLink className="h-4 w-4 mr-2" />
              GitHub Repository
            </Button>
            <Button variant="outline">
              <ExternalLink className="h-4 w-4 mr-2" />
              Community Support
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
