# Knowledge Space Visibility Feature

This document describes the implementation of the Knowledge Space visibility toggle feature that allows users to switch between public and private modes for their Knowledge Spaces.

## Overview

The visibility feature enables users to:
- Toggle Knowledge Spaces between public and private modes
- See clear visual indicators of visibility status
- Understand the implications of making spaces public
- Manage visibility settings through a dedicated settings page

## Implementation Details

### 1. Database Schema ✅

The database already includes the necessary fields in the `SearchSpace` model:
- `is_public: Boolean` - Controls whether the space is publicly discoverable
- `allow_all_users: Boolean` - Controls whether all users have access
- Default visibility is **private** (`is_public = False`)

### 2. Backend API ✅

#### New Endpoints
- `PUT /api/v1/searchspaces/{id}/visibility` - Simple visibility toggle endpoint
- `PUT /api/v1/searchspaces/{id}/access-settings` - Comprehensive access control (existing)

#### Access Control Logic ✅
- Only owners can change visibility settings
- Public spaces are discoverable by all users (when enabled in config)
- Private spaces are only accessible to owners and explicitly shared users
- Proper authorization checks throughout the system

### 3. Frontend Components ✅

#### Core Components
1. **VisibilityToggle** (`/components/visibility-toggle.tsx`)
   - Main toggle component with confirmation dialogs
   - Handles API calls and error states
   - Shows help text and implications

2. **VisibilityIndicator** (`/components/visibility-indicator.tsx`)
   - Reusable badge component for showing visibility status
   - Multiple sizes and display options
   - Consistent styling across the app

#### Integration Points
1. **Knowledge Space Form** (`/components/search-space-form.tsx`)
   - Added visibility toggle to creation/edit forms
   - Shows contextual help when making spaces public

2. **Dashboard** (`/app/dashboard/page.tsx`)
   - Visual indicators on Knowledge Space cards
   - Clear public/private badges

3. **Settings Page** (`/app/dashboard/[search_space_id]/settings/page.tsx`)
   - Dedicated settings page for each Knowledge Space
   - Comprehensive visibility management
   - Basic information editing and danger zone

### 4. User Experience Features ✅

#### Visual Feedback
- 🌐 Globe icon for public spaces
- 🔒 Lock icon for private spaces
- Color-coded badges (blue for public, gray for private)
- Consistent iconography throughout the app

#### Confirmation & Help
- Confirmation dialog when making spaces public
- Clear explanation of implications
- Help text in forms and settings
- Contextual information about visibility modes

#### Error Handling
- Proper error messages for API failures
- Loading states during operations
- Toast notifications for success/failure
- Graceful degradation

## File Structure

```
surfsense_backend/
├── app/
│   ├── routes/search_spaces_routes.py     # API endpoints
│   ├── schemas/access_control.py          # Pydantic schemas
│   ├── utils/access_control.py           # Access control logic
│   └── db.py                             # Database models

surfsense_web/
├── components/
│   ├── visibility-toggle.tsx             # Main toggle component
│   ├── visibility-indicator.tsx          # Status indicator
│   ├── search-space-form.tsx            # Updated form
│   └── demo/visibility-demo.tsx          # Demo component
├── app/
│   ├── dashboard/
│   │   ├── page.tsx                      # Updated dashboard
│   │   ├── searchspaces/page.tsx         # Create page
│   │   └── [search_space_id]/
│   │       └── settings/page.tsx         # Settings page
│   └── demo/visibility/page.tsx          # Demo page
└── hooks/
    └── use-search-spaces.ts              # Updated hook
```

## Usage Examples

### Creating a Public Knowledge Space
```typescript
const handleCreate = async (data: {
  name: string;
  description: string;
  is_public?: boolean;
}) => {
  // API call includes is_public field
  const response = await fetch('/api/v1/searchspaces', {
    method: 'POST',
    body: JSON.stringify(data)
  });
};
```

### Toggling Visibility
```typescript
const toggleVisibility = async (searchSpaceId: number, isPublic: boolean) => {
  const response = await fetch(`/api/v1/searchspaces/${searchSpaceId}/visibility`, {
    method: 'PUT',
    body: JSON.stringify({ is_public: isPublic })
  });
};
```

### Using Components
```tsx
// Visibility indicator
<VisibilityIndicator isPublic={space.is_public} size="sm" />

// Visibility toggle
<VisibilityToggle
  searchSpaceId={space.id}
  isPublic={space.is_public}
  onVisibilityChange={handleVisibilityChange}
/>
```

## Configuration

The feature respects system-wide configuration:
- `ALLOW_PUBLIC_WORKSPACES` - Enables/disables public workspaces globally
- `ENABLE_WORKSPACE_SHARING` - Controls workspace sharing features

## Security Considerations

1. **Authorization**: Only owners can change visibility settings
2. **Access Control**: Public spaces still respect read/write permissions
3. **Configuration**: System admins can disable public workspaces
4. **Validation**: Proper input validation on all endpoints

## Testing

To test the feature:
1. Visit `/demo/visibility` to see the demo page
2. Create a new Knowledge Space and toggle visibility
3. Check the dashboard for visual indicators
4. Use the settings page to manage existing spaces

## Future Enhancements

Potential improvements:
- Bulk visibility operations
- Visibility history/audit log
- Advanced sharing options
- Public space discovery page
- Analytics for public spaces

## Troubleshooting

Common issues:
- **Toggle not working**: Check API endpoint and authentication
- **Visual indicators missing**: Verify component imports and data structure
- **Permissions errors**: Ensure user has owner/admin access
- **Configuration issues**: Check system-wide settings

## Demo

Visit `/demo/visibility` to see a working demonstration of all visibility features.
