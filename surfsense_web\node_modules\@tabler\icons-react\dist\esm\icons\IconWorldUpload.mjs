/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWorldUpload = createReactComponent("outline", "world-upload", "IconWorldUpload", [["path", { "d": "M21 12a9 9 0 1 0 -9 9", "key": "svg-0" }], ["path", { "d": "M3.6 9h16.8", "key": "svg-1" }], ["path", { "d": "M3.6 15h8.4", "key": "svg-2" }], ["path", { "d": "M11.578 3a17 17 0 0 0 0 18", "key": "svg-3" }], ["path", { "d": "M12.5 3c1.719 2.755 2.5 5.876 2.5 9", "key": "svg-4" }], ["path", { "d": "M18 21v-7m3 3l-3 -3l-3 3", "key": "svg-5" }]]);

export { IconWorldUpload as default };
//# sourceMappingURL=IconWorldUpload.mjs.map
