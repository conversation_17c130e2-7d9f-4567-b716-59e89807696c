/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e1600460a404\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2VlcFxcRGVza3RvcFxcU3VyZlNlbnNlLW1haW5cXHN1cmZzZW5zZV93ZWJcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlMTYwMDQ2MGE0MDRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Roboto_arguments_subsets_latin_weight_400_500_700_display_swap_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Roboto\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"700\"],\"display\":\"swap\",\"variable\":\"--font-roboto\"}],\"variableName\":\"roboto\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Roboto\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-roboto\\\"}],\\\"variableName\\\":\\\"roboto\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Roboto_arguments_subsets_latin_weight_400_500_700_display_swap_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Roboto_arguments_subsets_latin_weight_400_500_700_display_swap_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./components/ui/sonner.tsx\");\n/* harmony import */ var _components_theme_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme/theme-provider */ \"(rsc)/./components/theme/theme-provider.tsx\");\n/* harmony import */ var fumadocs_ui_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! fumadocs-ui/provider */ \"(rsc)/./node_modules/fumadocs-ui/dist/provider/index.js\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"SurfSense – Customizable AI Research & Knowledge Management Assistant\",\n    description: \"SurfSense is an AI-powered research assistant that integrates with tools like Notion, GitHub, Slack, and more to help you efficiently manage, search, and chat with your documents. Generate podcasts, perform hybrid search, and unlock insights from your knowledge base.\",\n    keywords: [\n        \"SurfSense\",\n        \"AI research assistant\",\n        \"AI knowledge management\",\n        \"AI document assistant\",\n        \"customizable AI assistant\",\n        \"notion integration\",\n        \"slack integration\",\n        \"github integration\",\n        \"hybrid search\",\n        \"vector search\",\n        \"RAG\",\n        \"LangChain\",\n        \"FastAPI\",\n        \"LLM apps\",\n        \"AI document chat\",\n        \"knowledge management AI\",\n        \"AI-powered document search\",\n        \"personal AI assistant\",\n        \"AI research tools\",\n        \"AI podcast generator\",\n        \"AI knowledge base\",\n        \"AI document assistant tools\",\n        \"AI-powered search assistant\"\n    ],\n    openGraph: {\n        title: \"SurfSense – AI Research & Knowledge Management Assistant\",\n        description: \"Connect your documents and tools like Notion, Slack, GitHub, and more to your private AI assistant. SurfSense offers powerful search, document chat, podcast generation, and RAG APIs to enhance your workflow.\",\n        url: \"https://surfsense.net\",\n        siteName: \"SurfSense\",\n        type: \"website\",\n        images: [\n            {\n                url: \"https://surfsense.net/og-image.png\",\n                width: 1200,\n                height: 630,\n                alt: \"SurfSense AI Research Assistant\"\n            }\n        ],\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"SurfSense – AI Assistant for Research & Knowledge Management\",\n        description: \"Have your own NotebookLM or Perplexity, but better. SurfSense connects external tools, allows chat with your documents, and generates fast, high-quality podcasts.\",\n        creator: \"https://surfsense.net\",\n        site: \"https://surfsense.net\",\n        images: [\n            {\n                url: \"https://surfsense.net/og-image-twitter.png\",\n                width: 1200,\n                height: 630,\n                alt: \"SurfSense AI Assistant Preview\"\n            }\n        ]\n    }\n};\nasync function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)((next_font_google_target_css_path_app_layout_tsx_import_Roboto_arguments_subsets_latin_weight_400_500_700_display_swap_variable_font_roboto_variableName_roboto___WEBPACK_IMPORTED_MODULE_6___default().className), \"bg-white dark:bg-black antialiased h-full w-full\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_theme_provider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                defaultTheme: \"light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(fumadocs_ui_provider__WEBPACK_IMPORTED_MODULE_5__.RootProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\layout.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\layout.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\layout.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\layout.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\layout.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\SurfSense-main\\surfsense_web\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/theme/theme-provider.tsx":
/*!*********************************************!*\
  !*** ./components/theme/theme-provider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\SurfSense-main\\surfsense_web\\components\\theme\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\SurfSense-main\\surfsense_web\\components\\ui\\sonner.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGtlZXBcXERlc2t0b3BcXFN1cmZTZW5zZS1tYWluXFxzdXJmc2Vuc2Vfd2ViXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckeep%5CDesktop%5CSurfSense-main%5Csurfsense_web%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5Ckeep%5CDesktop%5CSurfSense-main%5Csurfsense_web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckeep%5CDesktop%5CSurfSense-main%5Csurfsense_web%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5Ckeep%5CDesktop%5CSurfSense-main%5Csurfsense_web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckeep%5CDesktop%5CSurfSense-main%5Csurfsense_web%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5Ckeep%5CDesktop%5CSurfSense-main%5Csurfsense_web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Ctheme%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Ctheme%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme/theme-provider.tsx */ \"(rsc)/./components/theme/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(rsc)/./components/ui/sonner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/provider/index.js */ \"(rsc)/./node_modules/fumadocs-ui/dist/provider/index.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2tlZXAlNUMlNUNEZXNrdG9wJTVDJTVDU3VyZlNlbnNlLW1haW4lNUMlNUNzdXJmc2Vuc2Vfd2ViJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDa2VlcCU1QyU1Q0Rlc2t0b3AlNUMlNUNTdXJmU2Vuc2UtbWFpbiU1QyU1Q3N1cmZzZW5zZV93ZWIlNUMlNUNjb21wb25lbnRzJTVDJTVDdGhlbWUlNUMlNUN0aGVtZS1wcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUaGVtZVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2tlZXAlNUMlNUNEZXNrdG9wJTVDJTVDU3VyZlNlbnNlLW1haW4lNUMlNUNzdXJmc2Vuc2Vfd2ViJTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDc29ubmVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDa2VlcCU1QyU1Q0Rlc2t0b3AlNUMlNUNTdXJmU2Vuc2UtbWFpbiU1QyU1Q3N1cmZzZW5zZV93ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNmdW1hZG9jcy11aSU1QyU1Q2Rpc3QlNUMlNUNwcm92aWRlciU1QyU1Q2luZGV4LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUm9vdFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2tlZXAlNUMlNUNEZXNrdG9wJTVDJTVDU3VyZlNlbnNlLW1haW4lNUMlNUNzdXJmc2Vuc2Vfd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJSb2JvdG8lNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ3ZWlnaHQlNUMlMjIlM0ElNUIlNUMlMjI0MDAlNUMlMjIlMkMlNUMlMjI1MDAlNUMlMjIlMkMlNUMlMjI3MDAlNUMlMjIlNUQlMkMlNUMlMjJkaXNwbGF5JTVDJTIyJTNBJTVDJTIyc3dhcCU1QyUyMiUyQyU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LXJvYm90byU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMnJvYm90byU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQXNLO0FBQ3RLO0FBQ0EsZ0tBQXFKO0FBQ3JKO0FBQ0EsOE1BQW1MIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxca2VlcFxcXFxEZXNrdG9wXFxcXFN1cmZTZW5zZS1tYWluXFxcXHN1cmZzZW5zZV93ZWJcXFxcY29tcG9uZW50c1xcXFx0aGVtZVxcXFx0aGVtZS1wcm92aWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxrZWVwXFxcXERlc2t0b3BcXFxcU3VyZlNlbnNlLW1haW5cXFxcc3VyZnNlbnNlX3dlYlxcXFxjb21wb25lbnRzXFxcXHVpXFxcXHNvbm5lci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlJvb3RQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGtlZXBcXFxcRGVza3RvcFxcXFxTdXJmU2Vuc2UtbWFpblxcXFxzdXJmc2Vuc2Vfd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxmdW1hZG9jcy11aVxcXFxkaXN0XFxcXHByb3ZpZGVyXFxcXGluZGV4LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Ctheme%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2tlZXAlNUMlNUNEZXNrdG9wJTVDJTVDU3VyZlNlbnNlLW1haW4lNUMlNUNzdXJmc2Vuc2Vfd2ViJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUEyRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxca2VlcFxcXFxEZXNrdG9wXFxcXFN1cmZTZW5zZS1tYWluXFxcXHN1cmZzZW5zZV93ZWJcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__ ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPW1keCZwYWdlRXh0ZW5zaW9ucz1tZCZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzIS4vYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2VlcFxcRGVza3RvcFxcU3VyZlNlbnNlLW1haW5cXHN1cmZzZW5zZV93ZWJcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ModernHeroWithGradients__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ModernHeroWithGradients */ \"(ssr)/./components/ModernHeroWithGradients.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./components/Footer.tsx\");\n/* harmony import */ var _hooks_use_current_user__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-current-user */ \"(ssr)/./hooks/use-current-user.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction HomePage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading } = (0,_hooks_use_current_user__WEBPACK_IMPORTED_MODULE_6__.useCurrentUser)();\n    const [isCheckingAuth, setIsCheckingAuth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // Only run on client-side\n            if (true) return;\n            // Check if user has a token in localStorage\n            const token = localStorage.getItem('surfsense_bearer_token');\n            if (token && user) {\n                // User is authenticated, redirect to dashboard\n                router.push('/dashboard');\n                return;\n            }\n            // If no token or user data loading is complete, stop checking\n            if (!token || !loading) {\n                setIsCheckingAuth(false);\n            }\n        }\n    }[\"HomePage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Show loading state while checking authentication\n    if (isCheckingAuth ||  false && 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 text-gray-900 dark:from-black dark:to-gray-900 dark:text-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                transition: {\n                    duration: 0.5\n                },\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Checking authentication...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 text-gray-900 dark:from-black dark:to-gray-900 dark:text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_3__.Navbar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernHeroWithGradients__WEBPACK_IMPORTED_MODULE_4__.ModernHeroWithGradients, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_5__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconBrandLinkedin_IconBrandTwitter_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconBrandDiscord,IconBrandGithub,IconBrandLinkedin,IconBrandTwitter!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconBrandTwitter.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconBrandLinkedin_IconBrandTwitter_tabler_icons_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=IconBrandDiscord,IconBrandGithub,IconBrandLinkedin,IconBrandTwitter!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconBrandLinkedin.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconBrandLinkedin_IconBrandTwitter_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconBrandDiscord,IconBrandGithub,IconBrandLinkedin,IconBrandTwitter!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconBrandGithub.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconBrandLinkedin_IconBrandTwitter_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconBrandDiscord,IconBrandGithub,IconBrandLinkedin,IconBrandTwitter!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconBrandDiscord.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\n\n\nfunction Footer() {\n    const pages = [\n        {\n            title: \"Privacy\",\n            href: \"/privacy\"\n        },\n        {\n            title: \"Terms\",\n            href: \"/terms\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-t border-neutral-100 dark:border-white/[0.1] px-8 py-20 bg-white dark:bg-neutral-950 w-full relative overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto text-sm text-neutral-500 justify-between items-start md:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center w-full relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mr-0 md:mr-4 md:flex mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-black dark:text-white ml-2\",\n                                    children: \"SurfSense\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"transition-colors flex sm:flex-row flex-col hover:text-text-neutral-800 text-neutral-600 dark:text-neutral-300 list-none gap-4\",\n                            children: pages.map((page, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"list-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        className: \"transition-colors hover:text-text-neutral-800\",\n                                        href: page.href,\n                                        children: page.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 17\n                                    }, this)\n                                }, \"pages\" + idx, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridLineHorizontal, {\n                            className: \"max-w-7xl mx-auto mt-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex sm:flex-row flex-col justify-between mt-8 items-center w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-500 dark:text-neutral-400 mb-8 sm:mb-0\",\n                            children: \"\\xa9 SurfSense 2025\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"https://x.com/mod_setter\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconBrandLinkedin_IconBrandTwitter_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-6 w-6 text-neutral-500 dark:text-neutral-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"https://www.linkedin.com/in/rohan-verma-sde/\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconBrandLinkedin_IconBrandTwitter_tabler_icons_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-6 w-6 text-neutral-500 dark:text-neutral-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"https://github.com/MODSetter\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconBrandLinkedin_IconBrandTwitter_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-6 w-6 text-neutral-500 dark:text-neutral-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"https://discord.gg/ejRNvftDp9\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconBrandLinkedin_IconBrandTwitter_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6 text-neutral-500 dark:text-neutral-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\nconst GridLineHorizontal = ({ className, offset })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            \"--background\": \"#ffffff\",\n            \"--color\": \"rgba(0, 0, 0, 0.2)\",\n            \"--height\": \"1px\",\n            \"--width\": \"5px\",\n            \"--fade-stop\": \"90%\",\n            \"--offset\": offset || \"200px\",\n            \"--color-dark\": \"rgba(255, 255, 255, 0.2)\",\n            maskComposite: \"exclude\"\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-[calc(100%+var(--offset))] h-[var(--height)]\", \"bg-[linear-gradient(to_right,var(--color),var(--color)_50%,transparent_0,transparent)]\", \"[background-size:var(--width)_var(--height)]\", \"[mask:linear-gradient(to_left,var(--background)_var(--fade-stop),transparent),_linear-gradient(to_right,var(--background)_var(--fade-stop),transparent),_linear-gradient(black,black)]\", \"[mask-composite:exclude]\", \"z-30\", \"dark:bg-[linear-gradient(to_right,var(--color-dark),var(--color-dark)_50%,transparent_0,transparent)]\", className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Footer.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Logo.tsx":
/*!*****************************!*\
  !*** ./components/Logo.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Logo: () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Logo auto */ \n\n\n\n\nconst Logo = ({ className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: \"/\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            src: \"/icon-128.png\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(className),\n            alt: \"logo\",\n            width: 128,\n            height: 128\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Logo.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Logo.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0xvZ28udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFDNkI7QUFDSDtBQUNLO0FBQ0U7QUFFMUIsTUFBTUksT0FBTyxDQUFDLEVBQUVDLFNBQVMsRUFBMEI7SUFDeEQscUJBQ0UsOERBQUNMLGtEQUFJQTtRQUNITSxNQUFLO2tCQUVMLDRFQUFDSixrREFBS0E7WUFDSkssS0FBSTtZQUNKRixXQUFXRiw4Q0FBRUEsQ0FBQ0U7WUFDZEcsS0FBSTtZQUNKQyxPQUFPO1lBQ1BDLFFBQVE7Ozs7Ozs7Ozs7O0FBSWhCLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2VlcFxcRGVza3RvcFxcU3VyZlNlbnNlLW1haW5cXHN1cmZzZW5zZV93ZWJcXGNvbXBvbmVudHNcXExvZ28udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiO1xuXG5leHBvcnQgY29uc3QgTG9nbyA9ICh7IGNsYXNzTmFtZSB9OiB7IGNsYXNzTmFtZT86IHN0cmluZyB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPExpbmtcbiAgICAgIGhyZWY9XCIvXCJcbiAgICA+XG4gICAgICA8SW1hZ2VcbiAgICAgICAgc3JjPVwiL2ljb24tMTI4LnBuZ1wiXG4gICAgICAgIGNsYXNzTmFtZT17Y24oY2xhc3NOYW1lKX1cbiAgICAgICAgYWx0PVwibG9nb1wiXG4gICAgICAgIHdpZHRoPXsxMjh9XG4gICAgICAgIGhlaWdodD17MTI4fVxuICAgICAgLz5cbiAgICA8L0xpbms+XG4gICk7XG59O1xuXG4iXSwibmFtZXMiOlsiTGluayIsIlJlYWN0IiwiSW1hZ2UiLCJjbiIsIkxvZ28iLCJjbGFzc05hbWUiLCJocmVmIiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ModernHeroWithGradients.tsx":
/*!************************************************!*\
  !*** ./components/ModernHeroWithGradients.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModernHeroWithGradients: () => (/* binding */ ModernHeroWithGradients)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconFileTypeDoc_tabler_icons_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=IconBrandDiscord,IconBrandGithub,IconFileTypeDoc!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFileTypeDoc.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconFileTypeDoc_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconBrandDiscord,IconBrandGithub,IconFileTypeDoc!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconBrandDiscord.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconFileTypeDoc_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconBrandDiscord,IconBrandGithub,IconFileTypeDoc!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconBrandGithub.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ ModernHeroWithGradients auto */ \n\n\n\n\n\nfunction ModernHeroWithGradients() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative h-full min-h-[50rem] w-full bg-gray-50 dark:bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative z-20 mx-auto w-full px-4 py-6 md:px-8 lg:px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative my-12 overflow-hidden rounded-3xl bg-white py-16 shadow-sm dark:bg-gray-900/80 dark:shadow-lg dark:shadow-purple-900/10 md:py-48 mx-auto w-full max-w-[95%] xl:max-w-[98%]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TopLines, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BottomLines, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SideLines, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TopGradient, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BottomGradient, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DarkModeGradient, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-20 flex flex-col items-center justify-center overflow-hidden rounded-3xl p-4 md:p-12 lg:p-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"https://github.com/MODSetter/SurfSense\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://trendshift.io/api/badge/repositories/13606\",\n                                        alt: \"MODSetter%2FSurfSense | Trendshift\",\n                                        style: {\n                                            width: \"250px\",\n                                            height: \"55px\"\n                                        },\n                                        width: 250,\n                                        height: 55\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/docs\",\n                                className: \"flex items-center gap-1 rounded-full border border-gray-200 bg-gradient-to-b from-gray-50 to-gray-100 px-4 py-1 text-center text-sm text-gray-800 shadow-sm dark:border-[#404040] dark:bg-gradient-to-b dark:from-[#5B5B5D] dark:to-[#262627] dark:text-white dark:shadow-inner dark:shadow-purple-500/10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconFileTypeDoc_tabler_icons_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-800 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Documentation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-4 mt-10 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 w-16\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_4__.Logo, {\n                                            className: \"rounded-md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"bg-gradient-to-b from-gray-800 to-gray-600 bg-clip-text py-4 text-center text-3xl text-transparent dark:from-white dark:to-purple-300 md:text-5xl lg:text-8xl\",\n                                        children: \"SurfSense\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mx-auto max-w-3xl py-6 text-center text-base text-gray-600 dark:text-neutral-300 md:text-lg lg:text-xl\",\n                                children: \"A Customizable AI Research Agent just like NotebookLM or Perplexity, but connected to external sources such as search engines (Tavily, LinkUp), Slack, Linear, Notion, YouTube, GitHub, Discord, and more.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-6 py-6 sm:flex-row\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"https://discord.gg/ejRNvftDp9\",\n                                        className: \"w-48 gap-1 rounded-full border border-gray-200 bg-gradient-to-b from-gray-50 to-gray-100 px-5 py-3 text-center text-sm font-medium text-gray-800 shadow-sm dark:border-[#404040] dark:bg-gradient-to-b dark:from-[#5B5B5D] dark:to-[#262627] dark:text-white dark:shadow-inner dark:shadow-purple-500/10 flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconFileTypeDoc_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Discord\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"https://github.com/MODSetter/SurfSense\",\n                                        className: \"w-48 gap-1 rounded-full border border-transparent bg-gray-800 px-5 py-3 text-center text-sm font-medium text-white shadow-sm hover:bg-gray-700 dark:bg-gradient-to-r dark:from-purple-700 dark:to-indigo-800 dark:text-white dark:hover:from-purple-600 dark:hover:to-indigo-700 flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBrandDiscord_IconBrandGithub_IconFileTypeDoc_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"GitHub\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 13,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n            lineNumber: 12,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n        lineNumber: 11,\n        columnNumber: 9\n    }, this);\n}\nconst TopLines = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"166\",\n        height: \"298\",\n        viewBox: \"0 0 166 298\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"aspect-square pointer-events-none absolute inset-x-0 top-0 h-[100px] w-full md:h-[200px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                y1: \"-0.5\",\n                x2: \"406\",\n                y2: \"-0.5\",\n                transform: \"matrix(0 1 1 0 1 -108)\",\n                stroke: \"url(#paint0_linear_254_143)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 85,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                y1: \"-0.5\",\n                x2: \"406\",\n                y2: \"-0.5\",\n                transform: \"matrix(0 1 1 0 34 -108)\",\n                stroke: \"url(#paint1_linear_254_143)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 92,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                y1: \"-0.5\",\n                x2: \"406\",\n                y2: \"-0.5\",\n                transform: \"matrix(0 1 1 0 67 -108)\",\n                stroke: \"url(#paint2_linear_254_143)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 99,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                y1: \"-0.5\",\n                x2: \"406\",\n                y2: \"-0.5\",\n                transform: \"matrix(0 1 1 0 100 -108)\",\n                stroke: \"url(#paint3_linear_254_143)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 106,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                y1: \"-0.5\",\n                x2: \"406\",\n                y2: \"-0.5\",\n                transform: \"matrix(0 1 1 0 133 -108)\",\n                stroke: \"url(#paint4_linear_254_143)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 113,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                y1: \"-0.5\",\n                x2: \"406\",\n                y2: \"-0.5\",\n                transform: \"matrix(0 1 1 0 166 -108)\",\n                stroke: \"url(#paint5_linear_254_143)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 120,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint0_linear_254_143\",\n                        x1: \"-7.42412e-06\",\n                        y1: \"0.500009\",\n                        x2: \"405\",\n                        y2: \"0.500009\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint1_linear_254_143\",\n                        x1: \"-7.42412e-06\",\n                        y1: \"0.500009\",\n                        x2: \"405\",\n                        y2: \"0.500009\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint2_linear_254_143\",\n                        x1: \"-7.42412e-06\",\n                        y1: \"0.500009\",\n                        x2: \"405\",\n                        y2: \"0.500009\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint3_linear_254_143\",\n                        x1: \"-7.42412e-06\",\n                        y1: \"0.500009\",\n                        x2: \"405\",\n                        y2: \"0.500009\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint4_linear_254_143\",\n                        x1: \"-7.42412e-06\",\n                        y1: \"0.500009\",\n                        x2: \"405\",\n                        y2: \"0.500009\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint5_linear_254_143\",\n                        x1: \"-7.42412e-06\",\n                        y1: \"0.500009\",\n                        x2: \"405\",\n                        y2: \"0.500009\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 127,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n        lineNumber: 77,\n        columnNumber: 9\n    }, undefined);\n};\nconst BottomLines = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"445\",\n        height: \"418\",\n        viewBox: \"0 0 445 418\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"aspect-square pointer-events-none absolute inset-x-0 -bottom-20 z-20 h-[150px] w-full md:h-[300px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"139.5\",\n                y1: \"418\",\n                x2: \"139.5\",\n                y2: \"12\",\n                stroke: \"url(#paint0_linear_0_1)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 209,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"172.5\",\n                y1: \"418\",\n                x2: \"172.5\",\n                y2: \"12\",\n                stroke: \"url(#paint1_linear_0_1)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 216,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"205.5\",\n                y1: \"418\",\n                x2: \"205.5\",\n                y2: \"12\",\n                stroke: \"url(#paint2_linear_0_1)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 223,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"238.5\",\n                y1: \"418\",\n                x2: \"238.5\",\n                y2: \"12\",\n                stroke: \"url(#paint3_linear_0_1)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 230,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"271.5\",\n                y1: \"418\",\n                x2: \"271.5\",\n                y2: \"12\",\n                stroke: \"url(#paint4_linear_0_1)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 237,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"304.5\",\n                y1: \"418\",\n                x2: \"304.5\",\n                y2: \"12\",\n                stroke: \"url(#paint5_linear_0_1)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 244,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M1 149L109.028 235.894C112.804 238.931 115 243.515 115 248.361V417\",\n                stroke: \"url(#paint6_linear_0_1)\",\n                strokeOpacity: \"0.1\",\n                strokeWidth: \"1.5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 251,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M444 149L335.972 235.894C332.196 238.931 330 243.515 330 248.361V417\",\n                stroke: \"url(#paint7_linear_0_1)\",\n                strokeOpacity: \"0.1\",\n                strokeWidth: \"1.5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 257,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint0_linear_0_1\",\n                        x1: \"140.5\",\n                        y1: \"418\",\n                        x2: \"140.5\",\n                        y2: \"13\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint1_linear_0_1\",\n                        x1: \"173.5\",\n                        y1: \"418\",\n                        x2: \"173.5\",\n                        y2: \"13\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint2_linear_0_1\",\n                        x1: \"206.5\",\n                        y1: \"418\",\n                        x2: \"206.5\",\n                        y2: \"13\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint3_linear_0_1\",\n                        x1: \"239.5\",\n                        y1: \"418\",\n                        x2: \"239.5\",\n                        y2: \"13\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint4_linear_0_1\",\n                        x1: \"272.5\",\n                        y1: \"418\",\n                        x2: \"272.5\",\n                        y2: \"13\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint5_linear_0_1\",\n                        x1: \"305.5\",\n                        y1: \"418\",\n                        x2: \"305.5\",\n                        y2: \"13\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"gray\",\n                                className: \"dark:stop-color-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint6_linear_0_1\",\n                        x1: \"115\",\n                        y1: \"390.591\",\n                        x2: \"-59.1703\",\n                        y2: \"205.673\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0.481613\",\n                                stopColor: \"#E8E8E8\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"#E8E8E8\",\n                                stopOpacity: \"0\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint7_linear_0_1\",\n                        x1: \"330\",\n                        y1: \"390.591\",\n                        x2: \"504.17\",\n                        y2: \"205.673\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0.481613\",\n                                stopColor: \"#E8E8E8\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"#E8E8E8\",\n                                stopOpacity: \"0\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 263,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n        lineNumber: 201,\n        columnNumber: 9\n    }, undefined);\n};\nconst SideLines = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"1382\",\n        height: \"370\",\n        viewBox: \"0 0 1382 370\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"pointer-events-none absolute inset-0 z-30 h-full w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M268 115L181.106 6.97176C178.069 3.19599 173.485 1 168.639 1H0\",\n                stroke: \"url(#paint0_linear_337_46)\",\n                strokeOpacity: \"0.1\",\n                strokeWidth: \"1.5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 367,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M1114 115L1200.89 6.97176C1203.93 3.19599 1208.52 1 1213.36 1H1382\",\n                stroke: \"url(#paint1_linear_337_46)\",\n                strokeOpacity: \"0.1\",\n                strokeWidth: \"1.5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 373,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M268 255L181.106 363.028C178.069 366.804 173.485 369 168.639 369H0\",\n                stroke: \"url(#paint2_linear_337_46)\",\n                strokeOpacity: \"0.1\",\n                strokeWidth: \"1.5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 379,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M1114 255L1200.89 363.028C1203.93 366.804 1208.52 369 1213.36 369H1382\",\n                stroke: \"url(#paint3_linear_337_46)\",\n                strokeOpacity: \"0.1\",\n                strokeWidth: \"1.5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 385,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint0_linear_337_46\",\n                        x1: \"26.4087\",\n                        y1: \"1.00001\",\n                        x2: \"211.327\",\n                        y2: \"175.17\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0.481613\",\n                                stopColor: \"#E8E8E8\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"#E8E8E8\",\n                                stopOpacity: \"0\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint1_linear_337_46\",\n                        x1: \"1355.59\",\n                        y1: \"1.00001\",\n                        x2: \"1170.67\",\n                        y2: \"175.17\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0.481613\",\n                                stopColor: \"#E8E8E8\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"#E8E8E8\",\n                                stopOpacity: \"0\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint2_linear_337_46\",\n                        x1: \"26.4087\",\n                        y1: \"369\",\n                        x2: \"211.327\",\n                        y2: \"194.83\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0.481613\",\n                                stopColor: \"#E8E8E8\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"#E8E8E8\",\n                                stopOpacity: \"0\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"paint3_linear_337_46\",\n                        x1: \"1355.59\",\n                        y1: \"369\",\n                        x2: \"1170.67\",\n                        y2: \"194.83\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0.481613\",\n                                stopColor: \"#E8E8E8\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"#E8E8E8\",\n                                stopOpacity: \"0\",\n                                className: \"dark:stop-color-[#F8F8F8]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 391,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n        lineNumber: 359,\n        columnNumber: 9\n    }, undefined);\n};\nconst BottomGradient = ({ className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"851\",\n        height: \"595\",\n        viewBox: \"0 0 851 595\",\n        fill: \"none\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"pointer-events-none absolute -right-80 bottom-0 h-full w-full opacity-30 dark:opacity-100 dark:hidden\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M118.499 0H532.468L635.375 38.6161L665 194.625L562.093 346H0L24.9473 121.254L118.499 0Z\",\n                fill: \"url(#paint0_radial_254_132)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 454,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"radialGradient\", {\n                    id: \"paint0_radial_254_132\",\n                    cx: \"0\",\n                    cy: \"0\",\n                    r: \"1\",\n                    gradientUnits: \"userSpaceOnUse\",\n                    gradientTransform: \"translate(412.5 346) rotate(-91.153) scale(397.581 423.744)\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                            stopColor: \"#AAD3E9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                            offset: \"0.25\",\n                            stopColor: \"#7FB8D4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                            offset: \"0.573634\",\n                            stopColor: \"#5A9BB8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                            offset: \"1\",\n                            stopOpacity: \"0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 458,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n        lineNumber: 443,\n        columnNumber: 9\n    }, undefined);\n};\nconst TopGradient = ({ className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"1007\",\n        height: \"997\",\n        viewBox: \"0 0 1007 997\",\n        fill: \"none\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"pointer-events-none absolute -left-96 top-0 h-full w-full opacity-30 dark:opacity-100 dark:hidden\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M807 110.119L699.5 -117.546L8.5 -154L-141 246.994L-7 952L127 782.111L279 652.114L513 453.337L807 110.119Z\",\n                fill: \"url(#paint0_radial_254_135)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 490,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M807 110.119L699.5 -117.546L8.5 -154L-141 246.994L-7 952L127 782.111L279 652.114L513 453.337L807 110.119Z\",\n                fill: \"url(#paint1_radial_254_135)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 494,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"radialGradient\", {\n                        id: \"paint0_radial_254_135\",\n                        cx: \"0\",\n                        cy: \"0\",\n                        r: \"1\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        gradientTransform: \"translate(77.0001 15.8894) rotate(90.3625) scale(869.41 413.353)\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"#AAD3E9\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0.25\",\n                                stopColor: \"#7FB8D4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0.573634\",\n                                stopColor: \"#5A9BB8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"radialGradient\", {\n                        id: \"paint1_radial_254_135\",\n                        cx: \"0\",\n                        cy: \"0\",\n                        r: \"1\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        gradientTransform: \"translate(127.5 -31) rotate(1.98106) scale(679.906 715.987)\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                stopColor: \"#AAD3E9\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0.283363\",\n                                stopColor: \"#7FB8D4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0.573634\",\n                                stopColor: \"#5A9BB8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"1\",\n                                stopOpacity: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 498,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n        lineNumber: 479,\n        columnNumber: 9\n    }, undefined);\n};\nconst DarkModeGradient = ({ className } = {})=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"hidden dark:block\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -left-48 -top-48 h-[800px] w-[800px] rounded-full bg-purple-900/20 blur-[180px]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 533,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -right-48 -bottom-48 h-[800px] w-[800px] rounded-full bg-indigo-900/20 blur-[180px]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 534,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-1/2 top-1/2 h-[400px] w-[400px] -translate-x-1/2 -translate-y-1/2 rounded-full bg-purple-800/10 blur-[120px]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n                lineNumber: 535,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ModernHeroWithGradients.tsx\",\n        lineNumber: 532,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ModernHeroWithGradients.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_IconMenu2_IconUser_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=IconMenu2,IconUser,IconX!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconUser.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconMenu2_IconUser_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconMenu2,IconUser,IconX!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconX.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconMenu2_IconUser_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=IconMenu2,IconUser,IconX!=!@tabler/icons-react */ \"(ssr)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMenu2.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-motion-value-event.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./components/Logo.tsx\");\n/* harmony import */ var _theme_theme_toggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./theme/theme-toggle */ \"(ssr)/./components/theme/theme-toggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \n\n\n\n\n\n\n\n\nconst Navbar = ()=>{\n    const navItems = [\n        {\n            name: \"Docs\",\n            link: \"/docs\"\n        }\n    ];\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const { scrollY } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.useScroll)({\n        target: ref,\n        offset: [\n            \"start start\",\n            \"end start\"\n        ]\n    });\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useMotionValueEvent)(scrollY, \"change\", {\n        \"Navbar.useMotionValueEvent\": (latest)=>{\n            if (latest > 100) {\n                setVisible(true);\n            } else {\n                setVisible(false);\n            }\n        }\n    }[\"Navbar.useMotionValueEvent\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        ref: ref,\n        className: \"w-full fixed top-2 inset-x-0 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DesktopNav, {\n                visible: visible,\n                navItems: navItems\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MobileNav, {\n                visible: visible,\n                navItems: navItems\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, undefined);\n};\nconst DesktopNav = ({ navItems, visible })=>{\n    const [hoveredIndex, setHoveredIndex] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const handleGoogleLogin = ()=>{\n        // Redirect to the login page\n        window.location.href = '/login';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        onMouseLeave: ()=>setHoveredIndex(null),\n        animate: {\n            backdropFilter: \"blur(16px)\",\n            background: visible ? \"rgba(var(--background-rgb), 0.8)\" : \"rgba(var(--background-rgb), 0.6)\",\n            width: visible ? \"38%\" : \"80%\",\n            height: visible ? \"48px\" : \"64px\",\n            y: visible ? 8 : 0\n        },\n        initial: {\n            width: \"80%\",\n            height: \"64px\",\n            background: \"rgba(var(--background-rgb), 0.6)\"\n        },\n        transition: {\n            type: \"spring\",\n            stiffness: 400,\n            damping: 30\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"hidden lg:flex flex-row self-center items-center justify-between py-2 mx-auto px-6 rounded-full relative z-[60] backdrop-saturate-[1.8]\", visible ? \"border dark:border-white/10 border-gray-300/30\" : \"border-0\"),\n        style: {\n            \"--background-rgb\": \"var(--tw-dark) ? '0, 0, 0' : '255, 255, 255'\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_5__.Logo, {\n                        className: \"h-8 w-8 rounded-md\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"dark:text-white/90 text-gray-800 text-lg font-bold\",\n                        children: \"SurfSense\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"lg:flex flex-row items-center justify-end space-x-1 text-sm\",\n                        animate: {\n                            scale: visible ? 0.9 : 1\n                        },\n                        children: navItems.map((navItem, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                onHoverStart: ()=>setHoveredIndex(idx),\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    className: \"dark:text-white/90 text-gray-800 relative px-3 py-1.5 transition-colors\",\n                                    href: navItem.link,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10\",\n                                            children: navItem.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        hoveredIndex === idx && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            layoutId: \"menu-hover\",\n                                            className: \"absolute inset-0 rounded-full dark:bg-gradient-to-r dark:from-white/10 dark:to-white/20 bg-gradient-to-r from-gray-200 to-gray-300\",\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1.1,\n                                                background: \"var(--tw-dark) ? radial-gradient(circle at center, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 50%, transparent 100%) : radial-gradient(circle at center, rgba(0,0,0,0.05) 0%, rgba(0,0,0,0.03) 50%, transparent 100%)\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                scale: 0.8,\n                                                transition: {\n                                                    duration: 0.2\n                                                }\n                                            },\n                                            transition: {\n                                                type: \"spring\",\n                                                bounce: 0.4,\n                                                duration: 0.4\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, `nav-item-${idx}`, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_theme_toggle__WEBPACK_IMPORTED_MODULE_6__.ThemeTogglerComponent, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                        mode: \"popLayout\",\n                        initial: false,\n                        children: !visible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                scale: 0.8,\n                                opacity: 0\n                            },\n                            animate: {\n                                scale: 1,\n                                opacity: 1,\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 400,\n                                    damping: 25\n                                }\n                            },\n                            exit: {\n                                scale: 0.8,\n                                opacity: 0,\n                                transition: {\n                                    duration: 0.2\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleGoogleLogin,\n                                variant: \"outline\",\n                                className: \"hidden cursor-pointer md:flex items-center gap-2 rounded-full dark:bg-white/20 dark:hover:bg-white/30 dark:text-white bg-gray-100 hover:bg-gray-200 text-gray-800 border-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMenu2_IconUser_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\nconst MobileNav = ({ navItems, visible })=>{\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const handleGoogleLogin = ()=>{\n        // Redirect to the login page\n        window.location.href = \"./login\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n            animate: {\n                backdropFilter: \"blur(16px)\",\n                background: visible ? \"rgba(var(--background-rgb), 0.8)\" : \"rgba(var(--background-rgb), 0.6)\",\n                width: visible ? \"80%\" : \"90%\",\n                y: visible ? 0 : 8,\n                borderRadius: open ? \"24px\" : \"full\",\n                padding: \"8px 16px\"\n            },\n            initial: {\n                width: \"80%\",\n                background: \"rgba(var(--background-rgb), 0.6)\"\n            },\n            transition: {\n                type: \"spring\",\n                stiffness: 400,\n                damping: 30\n            },\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex relative flex-col lg:hidden w-full justify-between items-center max-w-[calc(100vw-2rem)] mx-auto z-50 backdrop-saturate-[1.8] rounded-full\", visible ? \"border border-solid dark:border-white/40 border-gray-300/30\" : \"border-0\"),\n            style: {\n                \"--background-rgb\": \"var(--tw-dark) ? '0, 0, 0' : '255, 255, 255'\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row justify-between items-center w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_5__.Logo, {\n                            className: \"h-8 w-8 rounded-md\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_theme_toggle__WEBPACK_IMPORTED_MODULE_6__.ThemeTogglerComponent, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, undefined),\n                                open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMenu2_IconUser_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"dark:text-white/90 text-gray-800\",\n                                    onClick: ()=>setOpen(!open)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMenu2_IconUser_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"dark:text-white/90 text-gray-800\",\n                                    onClick: ()=>setOpen(!open)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                    children: open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        transition: {\n                            type: \"spring\",\n                            stiffness: 400,\n                            damping: 30\n                        },\n                        className: \"flex rounded-3xl absolute top-16 dark:bg-black/80 bg-white/90 backdrop-blur-xl backdrop-saturate-[1.8] inset-x-0 z-50 flex-col items-start justify-start gap-4 w-full px-6 py-8\",\n                        children: [\n                            navItems.map((navItem, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: navItem.link,\n                                    onClick: ()=>setOpen(false),\n                                    className: \"relative dark:text-white/90 text-gray-800 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.span, {\n                                        className: \"block\",\n                                        children: navItem.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, `link=${idx}`, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 19\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleGoogleLogin,\n                                variant: \"outline\",\n                                className: \"flex cursor-pointer items-center gap-2 mt-4 w-full justify-center rounded-full dark:bg-white/20 dark:hover:bg-white/30 dark:text-white bg-gray-100 hover:bg-gray-200 text-gray-800 border-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMenu2_IconUser_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\Navbar.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme/theme-provider.tsx":
/*!*********************************************!*\
  !*** ./components/theme/theme-provider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\theme\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxrZWVwXFxEZXNrdG9wXFxTdXJmU2Vuc2UtbWFpblxcc3VyZnNlbnNlX3dlYlxcY29tcG9uZW50c1xcdGhlbWVcXHRoZW1lLXByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgVGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXIgfSBmcm9tIFwibmV4dC10aGVtZXNcIlxuaW1wb3J0IHR5cGUgeyBUaGVtZVByb3ZpZGVyUHJvcHMgfSBmcm9tIFwibmV4dC10aGVtZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/theme/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme/theme-toggle.tsx":
/*!*******************************************!*\
  !*** ./components/theme/theme-toggle.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeTogglerComponent: () => (/* binding */ ThemeTogglerComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_MoonIcon_SunIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MoonIcon,SunIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_MoonIcon_SunIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MoonIcon,SunIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeTogglerComponent auto */ \n\n\n\n\nfunction ThemeTogglerComponent() {\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [isClient, setIsClient] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ThemeTogglerComponent.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"ThemeTogglerComponent.useEffect\"], []);\n    return isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>{\n            theme === \"dark\" ? setTheme(\"light\") : setTheme(\"dark\");\n        },\n        className: \"w-8 h-8 flex hover:bg-gray-50 dark:hover:bg-white/[0.1] rounded-lg items-center cursor-pointer justify-center outline-none focus:ring-0 focus:outline-none active:ring-0 active:outline-none overflow-hidden\",\n        children: [\n            theme === \"light\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    x: 40,\n                    opacity: 0\n                },\n                animate: {\n                    x: 0,\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.3,\n                    ease: \"easeOut\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoonIcon_SunIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 flex-shrink-0  dark:text-neutral-500 text-neutral-700\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\theme\\\\theme-toggle.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 13\n                }, this)\n            }, theme, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\theme\\\\theme-toggle.tsx\",\n                lineNumber: 26,\n                columnNumber: 11\n            }, this),\n            theme === \"dark\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    x: 40,\n                    opacity: 0\n                },\n                animate: {\n                    x: 0,\n                    opacity: 1\n                },\n                transition: {\n                    ease: \"easeOut\",\n                    duration: 0.3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoonIcon_SunIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 flex-shrink-0 \"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\theme\\\\theme-toggle.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 13\n                }, this)\n            }, theme, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\theme\\\\theme-toggle.tsx\",\n                lineNumber: 46,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle theme\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\theme\\\\theme-toggle.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\theme\\\\theme-toggle.tsx\",\n        lineNumber: 19,\n        columnNumber: 7\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/theme/theme-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40\",\n            outline: \"border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        toastOptions: {\n            classNames: {\n                toast: \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n                description: \"group-[.toast]:text-muted-foreground\",\n                actionButton: \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground font-medium\",\n                cancelButton: \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground font-medium\"\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-current-user.ts":
/*!***********************************!*\
  !*** ./hooks/use-current-user.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCurrentUser: () => (/* binding */ useCurrentUser)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n\n\nfunction useCurrentUser() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchCurrentUser = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_1__.fetchWithAuth)(`${process.env.NEXT_PUBLIC_API_URL}/users/me`);\n            if (!response.ok) {\n                throw new Error('Failed to fetch current user');\n            }\n            const userData = await response.json();\n            setUser(userData);\n        } catch (err) {\n            setError(err.message);\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useCurrentUser.useEffect\": ()=>{\n            // Only fetch if we have a token\n            const token = localStorage.getItem('surfsense_bearer_token');\n            if (token) {\n                fetchCurrentUser();\n            } else {\n                setLoading(false);\n            }\n        }\n    }[\"useCurrentUser.useEffect\"], []);\n    const isAdmin = user?.is_superuser || false;\n    return {\n        user,\n        loading,\n        error,\n        isAdmin,\n        refetch: fetchCurrentUser\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-current-user.ts\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   fetchWithAuth: () => (/* binding */ fetchWithAuth),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl)\n/* harmony export */ });\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n\n/**\n * Custom fetch wrapper that handles authentication and redirects to home page on 401 Unauthorized\n * \n * @param url - The URL to fetch\n * @param options - Fetch options\n * @returns The fetch response\n */ async function fetchWithAuth(url, options = {}) {\n    // Only run on client-side\n    if (true) {\n        return fetch(url, options);\n    }\n    // Get token from localStorage\n    const token = localStorage.getItem('surfsense_bearer_token');\n    // Add authorization header if token exists\n    const headers = {\n        ...options.headers,\n        ...token && {\n            'Authorization': `Bearer ${token}`\n        }\n    };\n    // Make the request\n    const response = await fetch(url, {\n        ...options,\n        headers\n    });\n    // Handle 401 Unauthorized response\n    if (response.status === 401) {\n        // Show error toast\n        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Session expired. Please log in again.\");\n        // Clear token\n        localStorage.removeItem('surfsense_bearer_token');\n        // Redirect to home page\n        window.location.href = '/';\n        // Throw error to stop further processing\n        throw new Error('Unauthorized: Redirecting to login page');\n    }\n    return response;\n}\n/**\n * Get the full API URL\n * \n * @param path - The API path\n * @returns The full API URL\n */ function getApiUrl(path) {\n    // Remove leading slash if present\n    const cleanPath = path.startsWith('/') ? path.slice(1) : path;\n    // Get backend URL from environment variable\n    const baseUrl = process.env.NEXT_PUBLIC_FASTAPI_BACKEND_URL;\n    if (!baseUrl) {\n        console.error('NEXT_PUBLIC_FASTAPI_BACKEND_URL is not defined');\n        return '';\n    }\n    // Combine base URL and path\n    return `${baseUrl}/${cleanPath}`;\n}\n/**\n * API client with methods for common operations\n */ const apiClient = {\n    /**\n   * Make a GET request\n   * \n   * @param path - The API path\n   * @param options - Additional fetch options\n   * @returns The response data\n   */ async get (path, options = {}) {\n        const response = await fetchWithAuth(getApiUrl(path), {\n            method: 'GET',\n            ...options\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>null);\n            throw new Error(`API error: ${response.status} ${errorData?.detail || response.statusText}`);\n        }\n        return response.json();\n    },\n    /**\n   * Make a POST request\n   * \n   * @param path - The API path\n   * @param data - The request body\n   * @param options - Additional fetch options\n   * @returns The response data\n   */ async post (path, data, options = {}) {\n        const response = await fetchWithAuth(getApiUrl(path), {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                ...options.headers\n            },\n            body: JSON.stringify(data),\n            ...options\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>null);\n            throw new Error(`API error: ${response.status} ${errorData?.detail || response.statusText}`);\n        }\n        return response.json();\n    },\n    /**\n   * Make a PUT request\n   * \n   * @param path - The API path\n   * @param data - The request body\n   * @param options - Additional fetch options\n   * @returns The response data\n   */ async put (path, data, options = {}) {\n        const response = await fetchWithAuth(getApiUrl(path), {\n            method: 'PUT',\n            headers: {\n                'Content-Type': 'application/json',\n                ...options.headers\n            },\n            body: JSON.stringify(data),\n            ...options\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>null);\n            throw new Error(`API error: ${response.status} ${errorData?.detail || response.statusText}`);\n        }\n        return response.json();\n    },\n    /**\n   * Make a DELETE request\n   * \n   * @param path - The API path\n   * @param options - Additional fetch options\n   * @returns The response data\n   */ async delete (path, options = {}) {\n        const response = await fetchWithAuth(getApiUrl(path), {\n            method: 'DELETE',\n            ...options\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>null);\n            throw new Error(`API error: ${response.status} ${errorData?.detail || response.statusText}`);\n        }\n        return response.json();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGtlZXBcXERlc2t0b3BcXFN1cmZTZW5zZS1tYWluXFxzdXJmc2Vuc2Vfd2ViXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Ctheme%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Ctheme%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme/theme-provider.tsx */ \"(ssr)/./components/theme/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(ssr)/./components/ui/sonner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/provider/index.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/provider/index.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Ctheme%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2tlZXAlNUMlNUNEZXNrdG9wJTVDJTVDU3VyZlNlbnNlLW1haW4lNUMlNUNzdXJmc2Vuc2Vfd2ViJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUEyRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxca2VlcFxcXFxEZXNrdG9wXFxcXFN1cmZTZW5zZS1tYWluXFxcXHN1cmZzZW5zZV93ZWJcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2tlZXAlNUMlNUNEZXNrdG9wJTVDJTVDU3VyZlNlbnNlLW1haW4lNUMlNUNzdXJmc2Vuc2Vfd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDa2VlcCU1QyU1Q0Rlc2t0b3AlNUMlNUNTdXJmU2Vuc2UtbWFpbiU1QyU1Q3N1cmZzZW5zZV93ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNrZWVwJTVDJTVDRGVza3RvcCU1QyU1Q1N1cmZTZW5zZS1tYWluJTVDJTVDc3VyZnNlbnNlX3dlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2tlZXAlNUMlNUNEZXNrdG9wJTVDJTVDU3VyZlNlbnNlLW1haW4lNUMlNUNzdXJmc2Vuc2Vfd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDaHR0cC1hY2Nlc3MtZmFsbGJhY2slNUMlNUNlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNrZWVwJTVDJTVDRGVza3RvcCU1QyU1Q1N1cmZTZW5zZS1tYWluJTVDJTVDc3VyZnNlbnNlX3dlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDa2VlcCU1QyU1Q0Rlc2t0b3AlNUMlNUNTdXJmU2Vuc2UtbWFpbiU1QyU1Q3N1cmZzZW5zZV93ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2tlZXAlNUMlNUNEZXNrdG9wJTVDJTVDU3VyZlNlbnNlLW1haW4lNUMlNUNzdXJmc2Vuc2Vfd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbWV0YWRhdGElNUMlNUNtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNrZWVwJTVDJTVDRGVza3RvcCU1QyU1Q1N1cmZTZW5zZS1tYWluJTVDJTVDc3VyZnNlbnNlX3dlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUEwSjtBQUMxSjtBQUNBLDBPQUE2SjtBQUM3SjtBQUNBLDBPQUE2SjtBQUM3SjtBQUNBLG9SQUFtTDtBQUNuTDtBQUNBLHdPQUE0SjtBQUM1SjtBQUNBLDRQQUF1SztBQUN2SztBQUNBLGtRQUEwSztBQUMxSztBQUNBLHNRQUEySyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxca2VlcFxcXFxEZXNrdG9wXFxcXFN1cmZTZW5zZS1tYWluXFxcXHN1cmZzZW5zZV93ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxca2VlcFxcXFxEZXNrdG9wXFxcXFN1cmZTZW5zZS1tYWluXFxcXHN1cmZzZW5zZV93ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxca2VlcFxcXFxEZXNrdG9wXFxcXFN1cmZTZW5zZS1tYWluXFxcXHN1cmZzZW5zZV93ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxca2VlcFxcXFxEZXNrdG9wXFxcXFN1cmZTZW5zZS1tYWluXFxcXHN1cmZzZW5zZV93ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxca2VlcFxcXFxEZXNrdG9wXFxcXFN1cmZTZW5zZS1tYWluXFxcXHN1cmZzZW5zZV93ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxrZWVwXFxcXERlc2t0b3BcXFxcU3VyZlNlbnNlLW1haW5cXFxcc3VyZnNlbnNlX3dlYlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXGFzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxrZWVwXFxcXERlc2t0b3BcXFxcU3VyZlNlbnNlLW1haW5cXFxcc3VyZnNlbnNlX3dlYlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxrZWVwXFxcXERlc2t0b3BcXFxcU3VyZlNlbnNlLW1haW5cXFxcc3VyZnNlbnNlX3dlYlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckeep%5C%5CDesktop%5C%5CSurfSense-main%5C%5Csurfsense_web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/fumadocs-core","vendor-chunks/next","vendor-chunks/fumadocs-ui","vendor-chunks/@radix-ui","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/@opentelemetry","vendor-chunks/motion-utils","vendor-chunks/@tabler","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/sonner","vendor-chunks/next-themes","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckeep%5CDesktop%5CSurfSense-main%5Csurfsense_web%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5Ckeep%5CDesktop%5CSurfSense-main%5Csurfsense_web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();