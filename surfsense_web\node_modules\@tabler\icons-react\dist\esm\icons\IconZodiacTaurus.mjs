/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZodiacTaurus = createReactComponent("outline", "zodiac-taurus", "IconZodiacTaurus", [["path", { "d": "M6 3a6 6 0 0 0 12 0", "key": "svg-0" }], ["path", { "d": "M12 15m-6 0a6 6 0 1 0 12 0a6 6 0 1 0 -12 0", "key": "svg-1" }]]);

export { IconZodiacTaurus as default };
//# sourceMappingURL=IconZodiacTaurus.mjs.map
