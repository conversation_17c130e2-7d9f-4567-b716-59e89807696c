"""Add workspace access control features

Revision ID: 12
Revises: 11
"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision: str = "12"
down_revision: Union[str, None] = "11"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema - add workspace access control features."""
    
    # Add access control columns to searchspaces table
    op.add_column('searchspaces', sa.Column('is_public', sa.<PERSON>(), nullable=False, server_default='false'))
    op.add_column('searchspaces', sa.Column('allow_all_users', sa.<PERSON>(), nullable=False, server_default='false'))
    
    # Create search_space_permissions table
    op.create_table('search_space_permissions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text('NOW()')),
        sa.Column('search_space_id', sa.Integer(), nullable=False),
        sa.Column('user_id', UUID(as_uuid=True), nullable=False),
        sa.Column('permission_level', sa.String(length=20), nullable=False, server_default='read'),
        sa.ForeignKeyConstraint(['search_space_id'], ['searchspaces.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('search_space_id', 'user_id', name='unique_user_search_space_permission')
    )
    
    # Create indexes
    op.create_index(op.f('ix_search_space_permissions_id'), 'search_space_permissions', ['id'], unique=False)
    op.create_index(op.f('ix_search_space_permissions_created_at'), 'search_space_permissions', ['created_at'], unique=False)


def downgrade() -> None:
    """Downgrade schema - remove workspace access control features."""
    
    # Drop indexes and table
    op.drop_index(op.f('ix_search_space_permissions_created_at'), table_name='search_space_permissions')
    op.drop_index(op.f('ix_search_space_permissions_id'), table_name='search_space_permissions')
    op.drop_table('search_space_permissions')
    
    # Drop access control columns from searchspaces table
    op.drop_column('searchspaces', 'allow_all_users')
    op.drop_column('searchspaces', 'is_public')
