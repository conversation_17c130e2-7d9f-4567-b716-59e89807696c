// @ts-nocheck -- skip type checking
import * as docs_3 from "../content/docs/manual-installation.mdx?collection=docs&hash=1749514655000"
import * as docs_2 from "../content/docs/installation.mdx?collection=docs&hash=1749514655000"
import * as docs_1 from "../content/docs/index.mdx?collection=docs&hash=1749514655000"
import * as docs_0 from "../content/docs/docker-installation.mdx?collection=docs&hash=1749514655000"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"docker-installation.mdx","absolutePath":"C:/Users/<USER>/Desktop/SurfSense-main/surfsense_web/content/docs/docker-installation.mdx"}, data: docs_0 }, { info: {"path":"index.mdx","absolutePath":"C:/Users/<USER>/Desktop/SurfSense-main/surfsense_web/content/docs/index.mdx"}, data: docs_1 }, { info: {"path":"installation.mdx","absolutePath":"C:/Users/<USER>/Desktop/SurfSense-main/surfsense_web/content/docs/installation.mdx"}, data: docs_2 }, { info: {"path":"manual-installation.mdx","absolutePath":"C:/Users/<USER>/Desktop/SurfSense-main/surfsense_web/content/docs/manual-installation.mdx"}, data: docs_3 }], [{"info":{"path":"meta.json","absolutePath":"C:/Users/<USER>/Desktop/SurfSense-main/surfsense_web/content/docs/meta.json"},"data":{"title":"Setup","pages":["---Setup---","index","installation","docker-installation","manual-installation"],"description":"The setup guide for Surfsense","root":true}}])