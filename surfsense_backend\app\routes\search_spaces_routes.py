from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from typing import List
from app.db import get_async_session, User, SearchSpace
from app.schemas import (
    SearchSpaceCreate, SearchSpaceUpdate, SearchSpaceRead, SearchSpaceWithPermissions,
    SearchSpaceAccessSettings, GrantPermissionRequest, RevokePermissionRequest,
    SearchSpaceVisibilityToggle
)
from app.users import current_active_user
from app.utils.access_control import (
    check_search_space_access, grant_search_space_permission,
    revoke_search_space_permission, update_search_space_access_settings
)
from app.config import config

router = APIRouter()

@router.post("/searchspaces/", response_model=SearchSpaceRead)
async def create_search_space(
    search_space: SearchSpaceCreate,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    try:
        db_search_space = SearchSpace(**search_space.model_dump(), user_id=user.id)
        session.add(db_search_space)
        await session.commit()
        await session.refresh(db_search_space)
        return db_search_space
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create search space: {str(e)}"
        )

@router.get("/searchspaces/", response_model=List[SearchSpaceRead])
async def read_search_spaces(
    skip: int = 0,
    limit: int = 200,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    try:
        # Build query based on access control settings
        query = select(SearchSpace).offset(skip).limit(limit)

        if config.ENABLE_WORKSPACE_SHARING:
            # User can see:
            # 1. Their own workspaces
            # 2. Workspaces they have explicit permissions for
            # 3. Public workspaces (if enabled)
            # 4. Workspaces that allow all users

            conditions = [SearchSpace.user_id == user.id]  # Own workspaces

            if config.ALLOW_PUBLIC_WORKSPACES:
                conditions.append(SearchSpace.is_public == True)  # Public workspaces

            conditions.append(SearchSpace.allow_all_users == True)  # All-users workspaces

            # Add workspaces with explicit permissions
            from sqlalchemy import or_, exists
            from app.db import SearchSpacePermission

            permission_condition = exists().where(
                SearchSpacePermission.search_space_id == SearchSpace.id,
                SearchSpacePermission.user_id == user.id
            )
            conditions.append(permission_condition)

            query = query.filter(or_(*conditions))
        else:
            # Only show user's own workspaces if sharing is disabled
            query = query.filter(SearchSpace.user_id == user.id)

        result = await session.execute(query)
        return result.scalars().all()
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch search spaces: {str(e)}"
        )

@router.get("/searchspaces/{search_space_id}", response_model=SearchSpaceRead)
async def read_search_space(
    search_space_id: int,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    try:
        search_space = await check_search_space_access(session, search_space_id, user, "read")
        return search_space

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch search space: {str(e)}"
        )

@router.put("/searchspaces/{search_space_id}", response_model=SearchSpaceRead)
async def update_search_space(
    search_space_id: int,
    search_space_update: SearchSpaceUpdate,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    try:
        db_search_space = await check_search_space_access(session, search_space_id, user, "write")
        update_data = search_space_update.model_dump(exclude_unset=True)

        # Handle access control fields separately
        access_fields = {"is_public", "allow_all_users"}
        access_updates = {k: v for k, v in update_data.items() if k in access_fields}
        regular_updates = {k: v for k, v in update_data.items() if k not in access_fields}

        # Update regular fields
        for key, value in regular_updates.items():
            setattr(db_search_space, key, value)

        # Update access control fields with validation
        if access_updates:
            db_search_space = await update_search_space_access_settings(
                session, search_space_id, user,
                is_public=access_updates.get("is_public"),
                allow_all_users=access_updates.get("allow_all_users")
            )
        else:
            await session.commit()
            await session.refresh(db_search_space)

        return db_search_space
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update search space: {str(e)}"
        )

@router.delete("/searchspaces/{search_space_id}", response_model=dict)
async def delete_search_space(
    search_space_id: int,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    try:
        # Only owners can delete search spaces
        db_search_space = await check_search_space_access(session, search_space_id, user, "admin")

        # Additional check: only the actual owner can delete (not just admin permission)
        if db_search_space.user_id != user.id:
            raise HTTPException(
                status_code=403,
                detail="Only the workspace owner can delete it"
            )

        await session.delete(db_search_space)
        await session.commit()
        return {"message": "Search space deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete search space: {str(e)}"
        )

# New endpoints for workspace access control

@router.post("/searchspaces/{search_space_id}/permissions")
async def grant_workspace_permission(
    search_space_id: int,
    permission_request: GrantPermissionRequest,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    """Grant permission to a user for a workspace"""
    try:
        permission = await grant_search_space_permission(
            session, search_space_id, permission_request.user_id,
            permission_request.permission_level, user
        )
        return {"message": "Permission granted successfully", "permission_id": permission.id}
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to grant permission: {str(e)}"
        )

@router.delete("/searchspaces/{search_space_id}/permissions")
async def revoke_workspace_permission(
    search_space_id: int,
    permission_request: RevokePermissionRequest,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    """Revoke permission from a user for a workspace"""
    try:
        success = await revoke_search_space_permission(
            session, search_space_id, permission_request.user_id, user
        )
        if success:
            return {"message": "Permission revoked successfully"}
        else:
            raise HTTPException(status_code=404, detail="Permission not found")
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to revoke permission: {str(e)}"
        )

@router.put("/searchspaces/{search_space_id}/access-settings")
async def update_workspace_access_settings(
    search_space_id: int,
    access_settings: SearchSpaceAccessSettings,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    """Update access settings for a workspace"""
    try:
        updated_space = await update_search_space_access_settings(
            session, search_space_id, user,
            is_public=access_settings.is_public,
            allow_all_users=access_settings.allow_all_users
        )
        return {"message": "Access settings updated successfully", "search_space": updated_space}
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update access settings: {str(e)}"
        )

@router.put("/searchspaces/{search_space_id}/visibility")
async def toggle_search_space_visibility(
    search_space_id: int,
    visibility_data: SearchSpaceVisibilityToggle,
    session: AsyncSession = Depends(get_async_session),
    user: User = Depends(current_active_user)
):
    """Toggle visibility of a search space between public and private"""
    try:
        updated_space = await update_search_space_access_settings(
            session, search_space_id, user,
            is_public=visibility_data.is_public,
            allow_all_users=None  # Don't change allow_all_users setting
        )

        visibility_status = "public" if visibility_data.is_public else "private"
        return {
            "message": f"Search space visibility updated to {visibility_status}",
            "search_space": updated_space,
            "is_public": visibility_data.is_public
        }
    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update visibility: {str(e)}"
        )