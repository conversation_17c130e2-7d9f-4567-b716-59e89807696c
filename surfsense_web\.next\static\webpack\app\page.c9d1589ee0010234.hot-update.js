"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./components/Navbar.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ModernHeroWithGradients__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ModernHeroWithGradients */ \"(app-pages-browser)/./components/ModernHeroWithGradients.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var _hooks_use_current_user__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-current-user */ \"(app-pages-browser)/./hooks/use-current-user.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading } = (0,_hooks_use_current_user__WEBPACK_IMPORTED_MODULE_6__.useCurrentUser)();\n    const [isCheckingAuth, setIsCheckingAuth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            // Only run on client-side\n            if (false) {}\n            // Check if user has a token in localStorage\n            const token = localStorage.getItem('surfsense_bearer_token');\n            if (token && user) {\n                // User is authenticated, redirect to dashboard\n                router.push('/dashboard');\n                return;\n            }\n            // If no token or user data loading is complete, stop checking\n            if (!token || !loading) {\n                setIsCheckingAuth(false);\n            }\n        }\n    }[\"HomePage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Show loading state while checking authentication\n    if (isCheckingAuth ||  true && localStorage.getItem('surfsense_bearer_token') && loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 text-gray-900 dark:from-black dark:to-gray-900 dark:text-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                transition: {\n                    duration: 0.5\n                },\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Checking authentication...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 text-gray-900 dark:from-black dark:to-gray-900 dark:text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_3__.Navbar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernHeroWithGradients__WEBPACK_IMPORTED_MODULE_4__.ModernHeroWithGradients, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_5__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SurfSense-main\\\\surfsense_web\\\\app\\\\page.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"PrGABcdpBAczPouHprZfKMbi9AE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_current_user__WEBPACK_IMPORTED_MODULE_6__.useCurrentUser\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});