/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconXxx = createReactComponent("outline", "xxx", "IconXxx", [["path", { "d": "M10 8l4 8", "key": "svg-0" }], ["path", { "d": "M10 16l4 -8", "key": "svg-1" }], ["path", { "d": "M17 8l4 8", "key": "svg-2" }], ["path", { "d": "M17 16l4 -8", "key": "svg-3" }], ["path", { "d": "M3 8l4 8", "key": "svg-4" }], ["path", { "d": "M3 16l4 -8", "key": "svg-5" }]]);

export { IconXxx as default };
//# sourceMappingURL=IconXxx.mjs.map
