/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconYoga = createReactComponent("outline", "yoga", "IconYoga", [["path", { "d": "M12 4m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-0" }], ["path", { "d": "M4 20h4l1.5 -3", "key": "svg-1" }], ["path", { "d": "M17 20l-1 -5h-5l1 -7", "key": "svg-2" }], ["path", { "d": "M4 10l4 -1l4 -1l4 1.5l4 1.5", "key": "svg-3" }]]);

export { IconYoga as default };
//# sourceMappingURL=IconYoga.mjs.map
