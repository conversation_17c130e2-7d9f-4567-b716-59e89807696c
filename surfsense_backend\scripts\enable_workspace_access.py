#!/usr/bin/env python3
"""
Script to enable workspace access for all users.
This script sets all workspaces to allow access from all users.

Usage:
    python scripts/enable_workspace_access.py [--mode=MODE]
    
Modes:
    all_users: Set all workspaces to allow_all_users=True (default)
    public: Set all workspaces to is_public=True
    both: Set both allow_all_users=True and is_public=True
    disable: Set both allow_all_users=False and is_public=False (private mode)
"""

import asyncio
import sys
import os
import argparse

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db import async_session_maker, SearchSpace
from sqlalchemy import update


async def enable_workspace_access(mode: str = "all_users"):
    """Enable workspace access based on the specified mode."""
    
    async with async_session_maker() as session:
        try:
            if mode == "all_users":
                # Set all workspaces to allow all users
                result = await session.execute(
                    update(SearchSpace).values(allow_all_users=True)
                )
                print(f"✅ Updated {result.rowcount} workspaces to allow access from all users")
                
            elif mode == "public":
                # Set all workspaces to public
                result = await session.execute(
                    update(SearchSpace).values(is_public=True)
                )
                print(f"✅ Updated {result.rowcount} workspaces to be public")
                
            elif mode == "both":
                # Set all workspaces to both allow all users and be public
                result = await session.execute(
                    update(SearchSpace).values(allow_all_users=True, is_public=True)
                )
                print(f"✅ Updated {result.rowcount} workspaces to be public and allow all users")
                
            elif mode == "disable":
                # Set all workspaces to private (disable sharing)
                result = await session.execute(
                    update(SearchSpace).values(allow_all_users=False, is_public=False)
                )
                print(f"✅ Updated {result.rowcount} workspaces to be private (owner-only access)")
                
            else:
                print(f"❌ Invalid mode: {mode}")
                print("Valid modes: all_users, public, both, disable")
                return False
                
            await session.commit()
            return True
            
        except Exception as e:
            await session.rollback()
            print(f"❌ Error updating workspaces: {str(e)}")
            return False


async def get_workspace_stats():
    """Get current workspace access statistics."""
    
    async with async_session_maker() as session:
        try:
            from sqlalchemy import select, func
            
            # Get total count
            total_result = await session.execute(
                select(func.count(SearchSpace.id))
            )
            total = total_result.scalar()
            
            # Get public count
            public_result = await session.execute(
                select(func.count(SearchSpace.id)).where(SearchSpace.is_public == True)
            )
            public = public_result.scalar()
            
            # Get all_users count
            all_users_result = await session.execute(
                select(func.count(SearchSpace.id)).where(SearchSpace.allow_all_users == True)
            )
            all_users = all_users_result.scalar()
            
            # Get both count
            both_result = await session.execute(
                select(func.count(SearchSpace.id)).where(
                    (SearchSpace.is_public == True) & (SearchSpace.allow_all_users == True)
                )
            )
            both = both_result.scalar()
            
            print(f"\n📊 Workspace Access Statistics:")
            print(f"   Total workspaces: {total}")
            print(f"   Public workspaces: {public}")
            print(f"   Allow all users: {all_users}")
            print(f"   Both public and all users: {both}")
            print(f"   Private workspaces: {total - max(public, all_users)}")
            
        except Exception as e:
            print(f"❌ Error getting workspace statistics: {str(e)}")


async def main():
    parser = argparse.ArgumentParser(description="Enable workspace access for all users")
    parser.add_argument(
        "--mode", 
        choices=["all_users", "public", "both", "disable", "stats"],
        default="all_users",
        help="Access mode to set (default: all_users)"
    )
    parser.add_argument(
        "--confirm",
        action="store_true",
        help="Skip confirmation prompt"
    )
    
    args = parser.parse_args()
    
    if args.mode == "stats":
        await get_workspace_stats()
        return
    
    # Show current stats first
    await get_workspace_stats()
    
    if not args.confirm:
        print(f"\n⚠️  This will modify ALL workspaces to use mode: {args.mode}")
        response = input("Are you sure you want to continue? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("❌ Operation cancelled")
            return
    
    print(f"\n🔄 Updating workspaces with mode: {args.mode}")
    success = await enable_workspace_access(args.mode)
    
    if success:
        print("\n📊 Updated statistics:")
        await get_workspace_stats()
        print(f"\n✅ Workspace access configuration completed successfully!")
        print("\n💡 Note: You may need to restart the application for changes to take full effect.")
    else:
        print("\n❌ Failed to update workspace access configuration")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
