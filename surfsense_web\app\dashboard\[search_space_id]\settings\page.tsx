"use client";

import { useState, useEffect } from "react";
import { useP<PERSON><PERSON>, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { VisibilityToggle } from "@/components/visibility-toggle";
import { VisibilityIndicator } from "@/components/visibility-indicator";
import { SearchSpaceForm } from "@/components/search-space-form";
import { 
  Settings, 
  ArrowLeft, 
  Edit3, 
  Trash2, 
  Users, 
  Shield,
  Globe,
  Lock
} from "lucide-react";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertD<PERSON>og<PERSON>rigger,
} from "@/components/ui/alert-dialog";

interface SearchSpace {
  id: number;
  name: string;
  description: string;
  is_public: boolean;
  allow_all_users: boolean;
  created_at: string;
  user_id: string;
}

export default function SearchSpaceSettingsPage() {
  const params = useParams();
  const router = useRouter();
  const searchSpaceId = parseInt(params.search_space_id as string);
  
  const [searchSpace, setSearchSpace] = useState<SearchSpace | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  useEffect(() => {
    fetchSearchSpace();
  }, [searchSpaceId]);

  const fetchSearchSpace = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FASTAPI_BACKEND_URL}/api/v1/searchspaces/${searchSpaceId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('surfsense_bearer_token')}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch search space');
      }

      const data = await response.json();
      setSearchSpace(data);
    } catch (error) {
      console.error('Error fetching search space:', error);
      toast.error("Failed to load search space");
      router.push('/dashboard');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateSearchSpace = async (data: { name: string; description: string; is_public?: boolean }) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FASTAPI_BACKEND_URL}/api/v1/searchspaces/${searchSpaceId}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('surfsense_bearer_token')}`,
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        throw new Error('Failed to update search space');
      }

      const updatedSpace = await response.json();
      setSearchSpace(updatedSpace);
      setIsEditing(false);
      
      toast.success("Search space updated successfully");
    } catch (error) {
      console.error('Error updating search space:', error);
      toast.error("Failed to update search space");
    }
  };

  const handleDeleteSearchSpace = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FASTAPI_BACKEND_URL}/api/v1/searchspaces/${searchSpaceId}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('surfsense_bearer_token')}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to delete search space');
      }

      toast.success("Search space deleted successfully");
      router.push('/dashboard');
    } catch (error) {
      console.error('Error deleting search space:', error);
      toast.error("Failed to delete search space");
    }
  };

  const handleVisibilityChange = (isPublic: boolean) => {
    if (searchSpace) {
      setSearchSpace({ ...searchSpace, is_public: isPublic });
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
            <div className="h-64 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!searchSpace) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-2xl font-bold">Search Space Not Found</h1>
          <p className="text-muted-foreground mt-2">
            The search space you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button onClick={() => router.push('/dashboard')} className="mt-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <motion.div 
      className="container mx-auto py-8"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/dashboard/${searchSpaceId}/documents`)}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to {searchSpace.name}
              </Button>
            </div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Settings className="h-8 w-8" />
              Settings
            </h1>
            <p className="text-muted-foreground">
              Manage your Knowledge Space settings and preferences
            </p>
          </div>
          <div className="flex items-center gap-2">
            <VisibilityIndicator isPublic={searchSpace.is_public} />
          </div>
        </div>

        <Separator />

        {/* Settings Content */}
        <div className="grid gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Edit3 className="h-5 w-5" />
                  Basic Information
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(!isEditing)}
                >
                  {isEditing ? "Cancel" : "Edit"}
                </Button>
              </CardTitle>
              <CardDescription>
                Update your Knowledge Space name and description
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isEditing ? (
                <SearchSpaceForm
                  isEditing={true}
                  initialData={{
                    name: searchSpace.name,
                    description: searchSpace.description || "",
                    is_public: searchSpace.is_public
                  }}
                  onSubmit={handleUpdateSearchSpace}
                  className="border-0 p-0 shadow-none"
                />
              ) : (
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Name</label>
                    <p className="text-lg font-medium">{searchSpace.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p className="text-sm">{searchSpace.description || "No description provided"}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Created</label>
                    <p className="text-sm">{new Date(searchSpace.created_at).toLocaleDateString()}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Visibility Settings */}
          <VisibilityToggle
            searchSpaceId={searchSpaceId}
            isPublic={searchSpace.is_public}
            onVisibilityChange={handleVisibilityChange}
          />

          {/* Danger Zone */}
          <Card className="border-destructive/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <Trash2 className="h-5 w-5" />
                Danger Zone
              </CardTitle>
              <CardDescription>
                Irreversible actions that will permanently affect your Knowledge Space
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Knowledge Space
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete the
                      "{searchSpace.name}" Knowledge Space and remove all associated
                      documents, chats, and data.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDeleteSearchSpace}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Delete Knowledge Space
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </CardContent>
          </Card>
        </div>
      </div>
    </motion.div>
  );
}
